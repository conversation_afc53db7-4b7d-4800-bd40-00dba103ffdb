#!/usr/bin/env python3
"""
图片分析智能体系统启动脚本
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    图片分析智能体系统                          ║
    ║                                                              ║
    ║    🤖 基于 AutoGen 0.6.2 的多智能体图片分析平台               ║
    ║    🚀 FastAPI + React + TypeScript                          ║
    ║    ✨ 支持流式输出和实时分析                                   ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误：需要 Python 3.8 或更高版本")
        sys.exit(1)
    print(f"✅ Python 版本: {sys.version.split()[0]}")

def check_node_version():
    """检查Node.js版本"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Node.js 版本: {version}")
            return True
        else:
            print("❌ 错误：未找到 Node.js")
            return False
    except FileNotFoundError:
        print("❌ 错误：未找到 Node.js")
        return False

def check_env_file():
    """检查环境变量文件"""
    env_file = Path("backend/.env")
    if not env_file.exists():
        print("⚠️  警告：未找到 backend/.env 文件")
        print("请复制 backend/.env.example 到 backend/.env 并配置 API Keys")
        
        # 自动复制示例文件
        example_file = Path("backend/.env.example")
        if example_file.exists():
            import shutil
            shutil.copy(example_file, env_file)
            print("✅ 已自动创建 .env 文件，请编辑并填入你的 API Keys")
        return False
    else:
        print("✅ 环境配置文件存在")
        return True

def install_backend_deps():
    """安装后端依赖"""
    print("\n📦 安装后端依赖...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "backend/requirements.txt"
        ], check=True, cwd=Path.cwd())
        print("✅ 后端依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 后端依赖安装失败")
        return False

def install_frontend_deps():
    """安装前端依赖"""
    print("\n📦 安装前端依赖...")
    try:
        subprocess.run("npm install", shell=True, check=True, cwd=Path("frontend"))
        print("✅ 前端依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 前端依赖安装失败")
        return False

def start_backend():
    """启动后端服务"""
    print("\n🚀 启动后端服务...")
    try:
        # 切换到backend目录并启动
        backend_process = subprocess.Popen([
            sys.executable, "main.py"
        ], cwd=Path("backend"))
        
        # 等待后端启动
        time.sleep(3)
        print("✅ 后端服务已启动 (http://localhost:8000)")
        return backend_process
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    print("\n🚀 启动前端服务...")
    try:
        frontend_process = subprocess.Popen([
            "npm", "start"
        ], cwd=Path("frontend"))
        
        # 等待前端启动
        time.sleep(5)
        print("✅ 前端服务已启动 (http://localhost:3000)")
        return frontend_process
    except Exception as e:
        print(f"❌ 前端服务启动失败: {e}")
        return None

def open_browser():
    """打开浏览器"""
    print("\n🌐 正在打开浏览器...")
    try:
        webbrowser.open("http://localhost:3000")
        print("✅ 浏览器已打开")
    except Exception as e:
        print(f"⚠️  无法自动打开浏览器: {e}")
        print("请手动访问: http://localhost:3000")

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    print("🔍 检查运行环境...")
    check_python_version()
    
    if not check_node_version():
        print("\n请安装 Node.js 16+ 后重试")
        sys.exit(1)
    
    env_ok = check_env_file()
    
    # 安装依赖
    if not install_backend_deps():
        sys.exit(1)
    
    if not install_frontend_deps():
        sys.exit(1)
    
    if not env_ok:
        print("\n⚠️  请先配置 backend/.env 文件中的 API Keys，然后重新运行此脚本")
        sys.exit(1)
    
    # 启动服务
    backend_process = start_backend()
    if not backend_process:
        sys.exit(1)
    
    frontend_process = start_frontend()
    if not frontend_process:
        backend_process.terminate()
        sys.exit(1)
    
    # 打开浏览器
    open_browser()
    
    print("\n" + "="*60)
    print("🎉 系统启动成功！")
    print("📱 前端地址: http://localhost:3000")
    print("🔧 后端地址: http://localhost:8000")
    print("📚 API 文档: http://localhost:8000/docs")
    print("="*60)
    print("\n按 Ctrl+C 停止服务")
    
    try:
        # 等待用户中断
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务...")
        if frontend_process:
            frontend_process.terminate()
        if backend_process:
            backend_process.terminate()
        print("✅ 服务已停止")

if __name__ == "__main__":
    main()
