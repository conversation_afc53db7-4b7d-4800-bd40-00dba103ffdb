# 安装指南

本指南将帮助您解决后端依赖安装失败的问题，特别是 AutoGen 与其他包的依赖冲突。

## 🚨 常见错误

### 依赖冲突错误
```
ERROR: Cannot install autogen-agentchat and pillow==10.1.0 because these package versions have conflicting dependencies.
ERROR: ResolutionImpossible: for help visit https://pip.pypa.io/en/latest/topics/dependency-resolution/#dealing-with-dependency-conflicts
```

这是因为 AutoGen 和 Pillow 的版本要求冲突导致的。

## 🔧 解决方案

### 方案1：使用自动修复脚本（推荐）

```bash
# 运行依赖冲突修复脚本
python fix_dependencies.py
```

脚本会提供4个选项：
1. **清理环境并分步安装**（推荐）
2. **使用约束文件安装**
3. **安装最小化依赖**
4. **全部尝试**

选择选项1，脚本会：
- 卸载冲突的包
- 按正确顺序重新安装
- 让 pip 自动解决版本冲突

### 方案2：手动解决

```bash
# 1. 卸载冲突的包
pip uninstall -y autogen-agentchat autogen-ext pillow pydantic

# 2. 安装基础依赖
pip install fastapi uvicorn python-multipart python-dotenv aiofiles httpx openai requests

# 3. 安装 Pillow（不指定版本）
pip install pillow

# 4. 安装 Pydantic
pip install "pydantic>=2.0.0,<3.0.0"

# 5. 尝试安装 AutoGen
pip install autogen-agentchat autogen-ext[openai]
```

### 方案3：使用虚拟环境

```bash
# 创建新的虚拟环境
python -m venv ui-automation-env

# 激活虚拟环境
# Windows:
ui-automation-env\Scripts\activate
# Linux/Mac:
source ui-automation-env/bin/activate

# 升级 pip
pip install --upgrade pip

# 安装依赖
cd backend
pip install -r requirements.txt
```

### 方案4：使用简化版本

如果 AutoGen 始终安装失败，系统会自动使用简化版本：

```bash
# 安装基础依赖
pip install fastapi uvicorn python-multipart python-dotenv pillow aiofiles pydantic httpx openai requests

# 启动简化版后端
cd backend
python main.py  # 会自动检测并使用简化版本
```

## 🧪 测试安装

### 测试基础功能
```bash
cd backend
python -c "import fastapi, uvicorn, PIL, pydantic, openai; print('✅ 基础依赖正常')"
```

### 测试 AutoGen
```bash
cd backend
python test_autogen.py
```

### 测试完整系统
```bash
python install_and_test.py
```

## 🔍 故障排除

### 1. 网络问题
```bash
# 使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ autogen-agentchat
```

### 2. 权限问题
```bash
# 使用用户安装
pip install --user autogen-agentchat
```

### 3. Python 版本问题
```bash
# 检查 Python 版本（需要 3.8+）
python --version

# 如果版本过低，使用 pyenv 或 conda 升级
```

### 4. pip 版本问题
```bash
# 升级 pip
python -m pip install --upgrade pip

# 清理缓存
pip cache purge
```

## 📋 系统要求

- **Python**: 3.8 或更高版本
- **pip**: 最新版本
- **内存**: 至少 4GB
- **网络**: 稳定的互联网连接

## 🚀 启动系统

### 配置环境变量
```bash
cd backend
cp .env.example .env
# 编辑 .env 文件，填入你的 API Keys
```

### 启动后端
```bash
cd backend
python main.py
```

系统会自动检测可用的智能体版本：
- ✅ **AutoGen 版本**：完整的多智能体协作功能
- ⚠️ **简化版本**：基于 OpenAI API 的基础功能

### 启动前端
```bash
cd frontend
npm install
npm start
```

## 💡 提示

1. **优先使用虚拟环境**，避免与系统包冲突
2. **保持 pip 最新版本**，新版本的依赖解析更好
3. **如果 AutoGen 安装失败**，简化版本也能提供基本功能
4. **网络不稳定时**，可以使用国内镜像源
5. **遇到问题时**，查看详细错误信息，通常包含解决提示

## 📞 获取帮助

如果仍然遇到问题：

1. 运行 `python fix_dependencies.py` 获取自动修复
2. 查看 `README.md` 中的故障排除部分
3. 检查 GitHub Issues 中的类似问题
4. 提交新的 Issue 并附上详细错误信息

记住：即使 AutoGen 安装失败，系统仍然可以使用简化版本正常运行！
