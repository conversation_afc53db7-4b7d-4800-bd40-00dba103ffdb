#!/usr/bin/env python3
"""
测试流式接口
"""
import asyncio
import aiohttp
import json

async def test_stream():
    """测试流式接口"""
    url = "http://localhost:8000/analyze/stream"
    params = {
        "image_path": "uploads/396005dd-7e48-45cf-84c6-2e8fba06c253.png",
        "user_message": "测试消息"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                print(f"状态码: {response.status}")
                print(f"响应头: {dict(response.headers)}")
                
                if response.status == 200:
                    async for line in response.content:
                        line_str = line.decode('utf-8').strip()
                        if line_str.startswith('data: '):
                            data = line_str[6:]  # 移除 'data: ' 前缀
                            if data == '[DONE]':
                                print("流式响应结束")
                                break
                            try:
                                message = json.loads(data)
                                print(f"收到消息: {message}")
                            except json.JSONDecodeError as e:
                                print(f"JSON解析错误: {e}, 原始数据: {data}")
                else:
                    text = await response.text()
                    print(f"错误响应: {text}")
                    
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_stream())
