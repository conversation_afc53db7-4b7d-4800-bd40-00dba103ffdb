import asyncio
import json
from contextlib import asynccontextmanager
from datetime import datetime
from fastapi import FastAPI, UploadFile, File, Form, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from fastapi.staticfiles import StaticFiles
from typing import Optional

from services.image_service import ImageService

# 尝试导入 AutoGen 版本，如果失败则使用简化版本
try:
    from services.agent_service import AgentService
    print("✅ 使用 AutoGen 版本的智能体服务")
    USE_AUTOGEN = True
except ImportError as e:
    print(f"⚠️  AutoGen 导入失败: {e}")
    print("🔄 使用简化版智能体服务")
    from simple_agent_service import SimpleAgentService as AgentService
    USE_AUTOGEN = False
from models.schemas import (
    AnalysisRequest, AnalysisResult, UploadResponse,
    StreamMessage, ErrorResponse, MessageType
)
from utils.config import Config

# 全局服务实例
image_service = ImageService()
agent_service = AgentService()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理

    Args:
        app: FastAPI应用实例
    """
    # 启动时的初始化
    print("🚀 启动图片分析智能体系统...")

    # 验证配置
    if not Config.validate_config():
        print("❌ 配置验证失败，请检查环境变量")
        raise Exception("Configuration validation failed")

    print("✅ 配置验证通过")
    print(f"📁 上传目录：{Config.UPLOAD_DIR}")
    print(f"🌐 服务地址：http://{Config.HOST}:{Config.PORT}")
    print(f"🤖 智能体模式：{'AutoGen' if USE_AUTOGEN else '简化版'}")

    yield

    # 关闭时的清理
    print("🔄 正在关闭智能体连接...")
    await agent_service.close()
    print("✅ 系统已安全关闭")

# 创建FastAPI应用
app = FastAPI(
    title="图片分析智能体系统",
    description=f"基于{'AutoGen 0.6.1' if USE_AUTOGEN else '简化版'}的多智能体图片分析系统",
    version="1.0.0",
    lifespan=lifespan
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务（用于访问上传的图片）
app.mount("/uploads", StaticFiles(directory=Config.UPLOAD_DIR), name="uploads")

@app.get("/")
async def root():
    """
    根路径接口

    Returns:
        dict: 系统基本信息
    """
    return {
        "message": "图片分析智能体系统",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """
    健康检查接口

    Returns:
        dict: 系统健康状态
    """
    return {
        "status": "healthy",
        "timestamp": "2025-01-09T00:00:00Z",
        "services": {
            "image_service": "ok",
            "agent_service": "ok"
        }
    }

@app.post("/upload", response_model=UploadResponse)
async def upload_image(file: UploadFile = File(...)):
    """
    上传图片接口

    Args:
        file: 上传的图片文件

    Returns:
        UploadResponse: 上传结果

    Raises:
        HTTPException: 上传失败时抛出HTTP异常
    """
    try:
        result = await image_service.save_uploaded_file(file)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"上传失败：{str(e)}")

@app.post("/analyze")
async def analyze_image(
    image_path: str = Form(...),
    user_message: Optional[str] = Form(None)
):
    """
    分析图片接口（非流式）

    Args:
        image_path: 图片文件路径
        user_message: 用户附加描述

    Returns:
        AnalysisResult: 完整的分析结果

    Raises:
        HTTPException: 分析失败时抛出HTTP异常
    """
    try:
        # 验证图片文件是否存在
        file_info = image_service.get_file_info(image_path)
        if not file_info:
            raise HTTPException(status_code=404, detail="图片文件不存在")

        # 创建分析请求
        request = AnalysisRequest(
            image_path=image_path,
            user_message=user_message
        )

        # 执行分析
        result = await agent_service.analyze_image(request)
        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分析失败：{str(e)}")

@app.get("/analyze/stream")
async def analyze_image_stream(
    image_path: str,
    user_message: Optional[str] = None
):
    """
    流式分析图片接口（SSE）

    Args:
        image_path: 图片文件路径
        user_message: 用户附加描述

    Returns:
        StreamingResponse: SSE流式响应

    Raises:
        HTTPException: 分析失败时抛出HTTP异常
    """
    try:
        # 验证图片文件是否存在
        file_info = image_service.get_file_info(image_path)
        if not file_info:
            raise HTTPException(status_code=404, detail="图片文件不存在")

        # 创建分析请求
        request = AnalysisRequest(
            image_path=image_path,
            user_message=user_message
        )

        async def event_generator():
            """
            SSE事件生成器

            Yields:
                str: SSE格式的事件数据
            """
            try:
                async for message in agent_service.analyze_image_stream(request):
                    # 转换为SSE格式
                    data = message.model_dump_json()
                    yield f"data: {data}\n\n"

                # 发送结束信号
                yield "data: [DONE]\n\n"

            except Exception as e:
                error_message = StreamMessage(
                    id="error",
                    type=MessageType.ERROR,
                    content=f"流式分析失败：{str(e)}",
                    timestamp=datetime.now()
                )
                yield f"data: {error_message.model_dump_json()}\n\n"
                yield "data: [DONE]\n\n"

        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"流式分析启动失败：{str(e)}")

@app.get("/files")
async def list_files():
    """
    列出所有上传的文件

    Returns:
        dict: 包含文件列表的字典

    Raises:
        HTTPException: 获取文件列表失败时抛出HTTP异常
    """
    try:
        files = image_service.list_uploaded_files()
        return {"files": files}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文件列表失败：{str(e)}")

@app.delete("/files/{file_path:path}")
async def delete_file(file_path: str):
    """
    删除文件接口

    Args:
        file_path: 要删除的文件路径

    Returns:
        dict: 删除结果消息

    Raises:
        HTTPException: 删除失败时抛出HTTP异常
    """
    try:
        success = image_service.delete_file(file_path)
        if success:
            return {"message": "文件删除成功"}
        else:
            raise HTTPException(status_code=404, detail="文件不存在")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除文件失败：{str(e)}")

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """
    全局异常处理器

    Args:
        request: HTTP请求对象
        exc: 异常对象

    Returns:
        ErrorResponse: 错误响应
    """
    return ErrorResponse(
        error=True,
        message=f"服务器内部错误：{str(exc)}",
        code="INTERNAL_ERROR"
    )

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host=Config.HOST,
        port=Config.PORT,
        reload=Config.DEBUG,
        log_level="info"
    )
