#!/usr/bin/env python3
"""
调试智能体 - 查看具体的数据传递
"""
import asyncio
import json
from simple_agents import SimpleElementRecognitionAgent, SimpleInteractionAnalysisAgent, SimpleUseCaseGenerationAgent
from utils.config import Config

async def debug_agents():
    """调试智能体"""
    config = Config()
    
    # 初始化智能体
    element_agent = SimpleElementRecognitionAgent(
        api_key=config.DEEPSEEK_API_KEY,
        base_url=config.DEEPSEEK_BASE_URL
    )
    
    interaction_agent = SimpleInteractionAnalysisAgent(
        api_key=config.DEEPSEEK_API_KEY,
        base_url=config.DEEPSEEK_BASE_URL
    )
    
    usecase_agent = SimpleUseCaseGenerationAgent(
        api_key=config.DEEPSEEK_API_KEY,
        base_url=config.DEEPSEEK_BASE_URL
    )
    
    try:
        # 测试元素识别
        print("🔍 测试元素识别...")
        elements_result = await element_agent.analyze_image(
            "uploads/396005dd-7e48-45cf-84c6-2e8fba06c253.png",
            "产品页面截图"
        )
        print(f"元素识别结果: {json.dumps(elements_result, ensure_ascii=False, indent=2)}")
        
        # 测试交互分析
        print("\n🔄 测试交互分析...")
        interactions_result = await interaction_agent.analyze_interactions(
            elements_result,
            "产品页面截图"
        )
        print(f"交互分析结果: {json.dumps(interactions_result, ensure_ascii=False, indent=2)}")
        
        # 测试用例生成
        print("\n📝 测试用例生成...")
        usecases_result = await usecase_agent.generate_use_cases(
            elements_result,
            interactions_result,
            "产品页面截图"
        )
        print(f"用例生成结果: {json.dumps(usecases_result, ensure_ascii=False, indent=2)}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
    finally:
        # 关闭客户端
        await element_agent.close()
        await interaction_agent.close()
        await usecase_agent.close()

if __name__ == "__main__":
    asyncio.run(debug_agents())
