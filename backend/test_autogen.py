#!/usr/bin/env python3
"""
测试 AutoGen 0.6.1 是否能正常工作
"""

import asyncio
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

async def test_autogen():
    """测试 AutoGen 基本功能"""
    try:
        print("🧪 测试 AutoGen 0.6.1...")
        
        # 测试导入
        from autogen_agentchat.agents import AssistantAgent
        from autogen_ext.models.openai import OpenAIChatCompletionClient
        print("✅ AutoGen 导入成功")
        
        # 检查 API 密钥
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("❌ 缺少 OPENAI_API_KEY 环境变量")
            return False
        
        # 创建模型客户端
        model_client = OpenAIChatCompletionClient(
            model="gpt-4o-mini",  # 使用更便宜的模型进行测试
            api_key=api_key
        )
        print("✅ 模型客户端创建成功")
        
        # 创建智能体
        agent = AssistantAgent(
            name="test_agent",
            model_client=model_client,
            system_message="你是一个测试智能体，请简短回复。"
        )
        print("✅ 智能体创建成功")
        
        # 测试简单对话
        result = await agent.run(task="请说'Hello AutoGen!'")
        print(f"✅ 智能体响应: {result.messages[-1].content}")
        
        # 关闭连接
        await model_client.close()
        print("✅ AutoGen 0.6.1 测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ AutoGen 导入失败: {e}")
        print("请运行: pip install autogen-agentchat==0.6.1 autogen-ext[openai]==0.6.1")
        return False
    except Exception as e:
        print(f"❌ AutoGen 测试失败: {e}")
        return False

async def test_vision_model():
    """测试视觉模型"""
    try:
        print("\n🖼️  测试视觉模型...")
        
        from autogen_agentchat.agents import AssistantAgent
        from autogen_ext.models.openai import OpenAIChatCompletionClient
        
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("❌ 缺少 OPENAI_API_KEY 环境变量")
            return False
        
        # 创建支持视觉的模型客户端
        model_client = OpenAIChatCompletionClient(
            model="gpt-4o",  # 支持视觉的模型
            api_key=api_key
        )
        
        # 创建视觉智能体
        vision_agent = AssistantAgent(
            name="vision_test_agent",
            model_client=model_client,
            system_message="你是一个图像分析智能体。"
        )
        print("✅ 视觉智能体创建成功")
        
        # 关闭连接
        await model_client.close()
        print("✅ 视觉模型测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 视觉模型测试失败: {e}")
        return False

async def test_team_chat():
    """测试团队对话"""
    try:
        print("\n👥 测试团队对话...")
        
        from autogen_agentchat.agents import AssistantAgent
        from autogen_agentchat.teams import RoundRobinGroupChat
        from autogen_ext.models.openai import OpenAIChatCompletionClient
        
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("❌ 缺少 OPENAI_API_KEY 环境变量")
            return False
        
        # 创建模型客户端
        model_client = OpenAIChatCompletionClient(
            model="gpt-4o-mini",
            api_key=api_key
        )
        
        # 创建两个智能体
        agent1 = AssistantAgent(
            name="agent1",
            model_client=model_client,
            system_message="你是智能体1，请简短回复。"
        )
        
        agent2 = AssistantAgent(
            name="agent2", 
            model_client=model_client,
            system_message="你是智能体2，请简短回复。"
        )
        
        # 创建团队
        team = RoundRobinGroupChat([agent1, agent2])
        print("✅ 团队创建成功")
        
        # 关闭连接
        await model_client.close()
        print("✅ 团队对话测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 团队对话测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始 AutoGen 0.6.1 兼容性测试...\n")
    
    tests = [
        ("基本功能", test_autogen()),
        ("视觉模型", test_vision_model()),
        ("团队对话", test_team_chat())
    ]
    
    results = []
    for test_name, test_coro in tests:
        print(f"📋 运行测试: {test_name}")
        result = await test_coro
        results.append((test_name, result))
        print()
    
    # 输出测试结果
    print("="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print("="*50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！AutoGen 0.6.1 可以正常使用。")
        print("\n💡 下一步:")
        print("1. 确保在 .env 文件中配置了正确的 API Keys")
        print("2. 运行 'python main.py' 启动后端服务")
    else:
        print("⚠️  部分测试失败，请检查 AutoGen 安装和配置。")
        print("\n🔧 故障排除:")
        print("1. 重新安装 AutoGen: pip install autogen-agentchat==0.6.1 autogen-ext[openai]==0.6.1")
        print("2. 检查 API Keys 配置")
        print("3. 确保网络连接正常")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 测试被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        exit(1)
