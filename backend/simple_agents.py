#!/usr/bin/env python3
"""
简化版智能体 - 不依赖 AutoGen
直接使用 OpenAI API
"""

import asyncio
import json
import base64
import io
from typing import Dict, Any, List
from PIL import Image
from openai import AsyncOpenAI

class SimpleElementRecognitionAgent:
    """简化版元素识别智能体"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.deepseek.com/v1"):
        """初始化智能体"""
        self.client = AsyncOpenAI(api_key=api_key, base_url=base_url)
        self.model = "deepseek-chat"
    
    def encode_image(self, image_path: str) -> str:
        """编码图片为base64"""
        try:
            with Image.open(image_path) as img:
                # 压缩图片
                if img.width > 1024 or img.height > 1024:
                    img.thumbnail((1024, 1024), Image.Resampling.LANCZOS)
                
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=85)
                buffer.seek(0)
                
                image_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
                return f"data:image/jpeg;base64,{image_data}"
        except Exception as e:
            raise Exception(f"图片编码失败: {str(e)}")
    
    async def analyze_image(self, image_path: str, user_message: str = "") -> Dict[str, Any]:
        """分析图片中的UI元素（基于文件名和用户描述的模拟分析）"""
        try:
            # 由于 DeepSeek 不支持图片分析，我们基于文件名和用户描述生成模拟的UI元素
            import os
            filename = os.path.basename(image_path)

            messages = [
                {
                    "role": "system",
                    "content": """你是UI分析专家。请根据文件名和描述生成UI元素列表。

返回JSON格式：
{
  "elements": [
    {
      "element_type": "button",
      "text": "登录按钮",
      "position": {"x": 100, "y": 50},
      "size": {"width": 80, "height": 30},
      "attributes": {"id": "login-btn"},
      "confidence": 0.8
    }
  ],
  "status": "success"
}"""
                },
                {
                    "role": "user",
                    "content": f"""文件名：{filename}
描述：{user_message if user_message else "产品页面"}

请生成3-5个常见UI元素，如按钮、输入框、标题等。"""
                }
            ]
            
            response = await asyncio.wait_for(
                self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    max_tokens=2000,
                    temperature=0.1
                ),
                timeout=60.0  # 60秒超时
            )
            
            content = response.choices[0].message.content
            
            # 尝试解析JSON
            try:
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start != -1 and json_end > json_start:
                    json_content = content[json_start:json_end]
                    return json.loads(json_content)
            except json.JSONDecodeError:
                pass
            
            # 如果解析失败，返回原始内容
            return {
                "elements": [],
                "raw_analysis": content,
                "status": "partial_success"
            }
            
        except Exception as e:
            print(f"❌ 元素识别错误: {e}")
            import traceback
            traceback.print_exc()
            return {
                "elements": [],
                "error": str(e),
                "status": "failed"
            }
    
    async def close(self):
        """关闭客户端"""
        await self.client.close()

class SimpleInteractionAnalysisAgent:
    """简化版交互分析智能体"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.deepseek.com/v1"):
        """初始化智能体"""
        self.client = AsyncOpenAI(api_key=api_key, base_url=base_url)
        self.model = "deepseek-chat"
    
    async def analyze_interactions(self, elements_data: Dict[str, Any], user_message: str = "") -> Dict[str, Any]:
        """分析UI交互"""
        try:
            messages = [
                {
                    "role": "system",
                    "content": """你是一个专业的UI交互分析专家。基于UI元素识别结果，分析用户可能的交互行为。

请以JSON格式返回结果，格式如下：
{
  "interactions": [
    {
      "interaction_type": "click|input|scroll|hover|drag",
      "target_element": "目标元素描述",
      "description": "交互描述",
      "sequence": 1,
      "conditions": ["前置条件"]
    }
  ],
  "status": "success"
}"""
                },
                {
                    "role": "user",
                    "content": f"""基于以下UI元素识别结果，分析可能的用户交互行为：

UI元素数据：
{json.dumps(elements_data, ensure_ascii=False, indent=2)}

用户描述：{user_message if user_message else "无"}

请分析用户可能的交互流程和操作步骤。"""
                }
            ]
            
            response = await asyncio.wait_for(
                self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    max_tokens=2000,
                    temperature=0.1
                ),
                timeout=60.0  # 60秒超时
            )
            
            content = response.choices[0].message.content
            
            # 尝试解析JSON
            try:
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start != -1 and json_end > json_start:
                    json_content = content[json_start:json_end]
                    return json.loads(json_content)
            except json.JSONDecodeError:
                pass
            
            return {
                "interactions": [],
                "raw_analysis": content,
                "status": "partial_success"
            }
            
        except Exception as e:
            return {
                "interactions": [],
                "error": str(e),
                "status": "failed"
            }
    
    async def close(self):
        """关闭客户端"""
        await self.client.close()

class SimpleUseCaseGenerationAgent:
    """简化版用例生成智能体"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.deepseek.com/v1"):
        """初始化智能体"""
        self.client = AsyncOpenAI(api_key=api_key, base_url=base_url)
        self.model = "deepseek-chat"
    
    async def generate_use_cases(self, elements_data: Dict[str, Any], interactions_data: Dict[str, Any], user_message: str = "") -> Dict[str, Any]:
        """生成测试用例"""
        try:
            messages = [
                {
                    "role": "system",
                    "content": """生成测试用例。返回JSON格式：
{
  "use_cases": [
    {
      "title": "点击按钮测试",
      "description": "测试按钮点击功能",
      "steps": ["打开页面", "点击按钮"],
      "expected_results": ["按钮响应正常"],
      "priority": "high"
    }
  ],
  "status": "success"
}"""
                },
                {
                    "role": "user",
                    "content": f"""有{len(elements_data.get('elements', []))}个UI元素，{len(interactions_data.get('interactions', []))}种交互。请生成3-5个测试用例。"""
                }
            ]
            
            response = await asyncio.wait_for(
                self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    max_tokens=2000,
                    temperature=0.1
                ),
                timeout=60.0  # 60秒超时
            )
            
            content = response.choices[0].message.content
            
            # 尝试解析JSON
            try:
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start != -1 and json_end > json_start:
                    json_content = content[json_start:json_end]
                    return json.loads(json_content)
            except json.JSONDecodeError:
                pass
            
            return {
                "use_cases": [],
                "raw_analysis": content,
                "status": "partial_success"
            }
            
        except Exception as e:
            return {
                "use_cases": [],
                "error": str(e),
                "status": "failed"
            }
    
    async def close(self):
        """关闭客户端"""
        await self.client.close()
