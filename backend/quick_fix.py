#!/usr/bin/env python3
"""
快速修复导入问题
"""

import os
import sys
from pathlib import Path

def fix_imports():
    """修复导入问题"""
    print("🔧 修复导入问题...")
    
    # 修复 agent_service.py 的导入
    agent_service_file = Path("services/agent_service.py")
    if agent_service_file.exists():
        print("  修复 agent_service.py...")
        with open(agent_service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有 List 导入
        if "from typing import" in content and "List" not in content:
            # 修复导入
            content = content.replace(
                "from typing import Dict, Any, AsyncGenerator",
                "from typing import Dict, Any, AsyncGenerator, List"
            )
            
            with open(agent_service_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print("  ✅ agent_service.py 已修复")
        else:
            print("  ✅ agent_service.py 无需修复")
    
    # 检查其他可能的问题
    files_to_check = [
        "agents/element_recognition.py",
        "agents/interaction_analysis.py", 
        "agents/usecase_generation.py",
        "simple_agents.py",
        "simple_agent_service.py"
    ]
    
    for file_path in files_to_check:
        file_obj = Path(file_path)
        if file_obj.exists():
            with open(file_obj, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有类型注解但缺少导入
            if "List[" in content and "from typing import" in content and "List" not in content.split('\n')[0:10]:
                print(f"  ⚠️  {file_path} 可能需要修复 List 导入")
            else:
                print(f"  ✅ {file_path} 导入正常")

def test_imports():
    """测试导入"""
    print("\n🧪 测试导入...")
    
    try:
        # 测试基础导入
        from utils.config import Config
        print("  ✅ 配置模块")
        
        from models.schemas import AnalysisRequest
        print("  ✅ 数据模型")
        
        from services.image_service import ImageService
        print("  ✅ 图片服务")
        
        # 测试智能体服务
        try:
            from services.agent_service import AgentService
            print("  ✅ AutoGen 智能体服务")
            return True
        except ImportError as e:
            print(f"  ❌ AutoGen 智能体服务: {e}")
            
            try:
                from simple_agent_service import SimpleAgentService
                print("  ✅ 简化版智能体服务")
                return True
            except ImportError as e2:
                print(f"  ❌ 简化版智能体服务: {e2}")
                return False
    
    except Exception as e:
        print(f"  ❌ 导入失败: {e}")
        return False

def create_minimal_main():
    """创建最小化的 main.py"""
    print("\n📝 创建最小化 main.py...")
    
    minimal_main = '''#!/usr/bin/env python3
"""
最小化版本的 main.py - 确保能够启动
"""

from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import uvicorn
import os
from pathlib import Path

# 创建应用
app = FastAPI(title="图片分析系统", version="1.0.0")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建上传目录
upload_dir = Path("uploads")
upload_dir.mkdir(exist_ok=True)
app.mount("/uploads", StaticFiles(directory=upload_dir), name="uploads")

@app.get("/")
async def root():
    return {"message": "图片分析系统", "status": "running", "version": "minimal"}

@app.get("/health")
async def health():
    return {"status": "healthy"}

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    try:
        file_path = upload_dir / file.filename
        with open(file_path, "wb") as f:
            content = await file.read()
            f.write(content)
        
        return {
            "success": True,
            "file_path": str(file_path),
            "file_size": len(content),
            "message": "文件上传成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/analyze")
async def analyze_image(
    image_path: str = Form(...),
    user_message: str = Form(None)
):
    # 返回示例数据
    return {
        "request_id": "demo",
        "image_path": image_path,
        "elements": [
            {"element_type": "button", "text": "示例按钮", "confidence": 0.9, "position": {"x": 100, "y": 200}, "size": {"width": 80, "height": 30}, "attributes": {}},
            {"element_type": "input", "text": "", "confidence": 0.8, "position": {"x": 50, "y": 100}, "size": {"width": 200, "height": 25}, "attributes": {"placeholder": "输入框"}}
        ],
        "interactions": [
            {"interaction_type": "click", "target_element": "button", "description": "点击按钮", "sequence": 1, "conditions": []},
            {"interaction_type": "input", "target_element": "input", "description": "输入文本", "sequence": 2, "conditions": []}
        ],
        "use_cases": [
            {"title": "基本功能测试", "description": "测试基本的UI交互", "steps": ["打开页面", "输入文本", "点击按钮"], "priority": "high"}
        ],
        "analysis_summary": "这是最小化版本的演示数据。请安装完整依赖获得真实的AI分析功能。",
        "created_at": "2025-01-09T00:00:00Z",
        "processing_time": 0.1
    }

if __name__ == "__main__":
    print("🚀 启动最小化版本...")
    print("⚠️  这是最小化版本，仅提供基本功能")
    print("💡 要获得完整功能，请解决依赖问题后使用完整版本")
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
    
    with open("main_minimal.py", 'w', encoding='utf-8') as f:
        f.write(minimal_main)
    
    print("✅ 最小化 main.py 已创建: main_minimal.py")

def main():
    """主函数"""
    print("🔧 快速修复脚本")
    print("="*40)
    
    # 修复导入问题
    fix_imports()
    
    # 测试导入
    import_success = test_imports()
    
    if not import_success:
        print("\n⚠️  导入仍然有问题，创建最小化版本...")
        create_minimal_main()
        print("\n💡 可以运行以下命令启动最小化版本:")
        print("   python main_minimal.py")
    else:
        print("\n🎉 导入问题已修复！")
        print("💡 现在可以运行:")
        print("   python main.py")
    
    print("\n📋 其他选项:")
    print("1. 运行完整测试: python test_imports.py")
    print("2. 解决依赖问题: python fix_dependencies.py")
    print("3. 查看安装指南: cat INSTALL_GUIDE.md")

if __name__ == "__main__":
    main()
'''
