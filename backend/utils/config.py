import os
from typing import List
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """应用程序配置类"""

    # OpenAI 配置
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    OPENAI_BASE_URL: str = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")

    # DeepSeek 配置
    DEEPSEEK_API_KEY: str = os.getenv("DEEPSEEK_API_KEY", "")
    DEEPSEEK_BASE_URL: str = os.getenv("DEEPSEEK_BASE_URL", "https://api.deepseek.com/v1")

    # UI-TARS 配置
    UITARS_API_KEY: str = os.getenv("UITARS_API_KEY", "")
    UITARS_BASE_URL: str = os.getenv("UITARS_BASE_URL", "https://api.ui-tars.com/v1")

    # 应用程序配置
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "uploads")
    MAX_FILE_SIZE: int = int(os.getenv("MAX_FILE_SIZE", "10485760"))  # 10MB 文件大小限制
    ALLOWED_EXTENSIONS: List[str] = os.getenv("ALLOWED_EXTENSIONS", "jpg,jpeg,png,gif,bmp,webp").split(",")

    # 服务器配置
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证必需的配置项"""
        required_keys = [
            "OPENAI_API_KEY",
            "DEEPSEEK_API_KEY",
            "UITARS_API_KEY"
        ]

        missing_keys = []
        for key in required_keys:
            if not getattr(cls, key):
                missing_keys.append(key)

        if missing_keys:
            print(f"缺少必需的配置项: {', '.join(missing_keys)}")
            return False

        return True

# 如果上传目录不存在则创建
os.makedirs(Config.UPLOAD_DIR, exist_ok=True)
