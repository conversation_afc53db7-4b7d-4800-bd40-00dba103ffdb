import os
import uuid
import aiofiles
from typing import Optional, List
from fastapi import UploadFile, HTTPException
from PIL import Image
import io

from utils.config import Config
from models.schemas import UploadResponse

class ImageService:
    """图片处理服务类"""

    def __init__(self):
        """初始化图片服务"""
        self.upload_dir = Config.UPLOAD_DIR
        self.max_file_size = Config.MAX_FILE_SIZE
        self.allowed_extensions = Config.ALLOWED_EXTENSIONS

    def _is_allowed_file(self, filename: str) -> bool:
        """
        检查文件扩展名是否允许

        Args:
            filename: 文件名

        Returns:
            bool: 是否允许该文件类型
        """
        if '.' not in filename:
            return False

        extension = filename.rsplit('.', 1)[1].lower()
        return extension in self.allowed_extensions

    def _validate_image(self, file_content: bytes) -> bool:
        """
        验证是否为有效图片

        Args:
            file_content: 文件内容字节

        Returns:
            bool: 是否为有效图片
        """
        try:
            with Image.open(io.BytesIO(file_content)) as img:
                # 验证图片格式
                img.verify()
                return True
        except Exception:
            return False
    
    async def save_uploaded_file(self, file: UploadFile) -> UploadResponse:
        """
        保存上传的文件

        Args:
            file: FastAPI上传文件对象

        Returns:
            UploadResponse: 上传结果响应
        """
        try:
            # 检查文件名
            if not file.filename:
                return UploadResponse(
                    success=False,
                    message="文件名不能为空"
                )

            # 检查文件扩展名
            if not self._is_allowed_file(file.filename):
                return UploadResponse(
                    success=False,
                    message=f"不支持的文件格式。支持的格式：{', '.join(self.allowed_extensions)}"
                )

            # 读取文件内容
            file_content = await file.read()

            # 检查文件大小
            if len(file_content) > self.max_file_size:
                return UploadResponse(
                    success=False,
                    message=f"文件大小超过限制（最大 {self.max_file_size // 1024 // 1024}MB）"
                )

            # 验证图片
            if not self._validate_image(file_content):
                return UploadResponse(
                    success=False,
                    message="无效的图片文件"
                )

            # 生成唯一文件名
            file_extension = file.filename.rsplit('.', 1)[1].lower()
            unique_filename = f"{uuid.uuid4()}.{file_extension}"
            file_path = os.path.join(self.upload_dir, unique_filename)

            # 保存文件
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(file_content)

            return UploadResponse(
                success=True,
                file_path=file_path,
                file_size=len(file_content),
                message="文件上传成功"
            )

        except Exception as e:
            return UploadResponse(
                success=False,
                message=f"文件上传失败：{str(e)}"
            )
    
    def get_file_info(self, file_path: str) -> Optional[dict]:
        """
        获取文件信息

        Args:
            file_path: 文件路径

        Returns:
            Optional[dict]: 文件信息字典，如果文件不存在或读取失败则返回None
        """
        try:
            if not os.path.exists(file_path):
                return None

            file_size = os.path.getsize(file_path)

            # 获取图片尺寸
            with Image.open(file_path) as img:
                width, height = img.size
                format_name = img.format

            return {
                "file_path": file_path,
                "file_size": file_size,
                "width": width,
                "height": height,
                "format": format_name
            }

        except Exception:
            return None

    def delete_file(self, file_path: str) -> bool:
        """
        删除文件

        Args:
            file_path: 文件路径

        Returns:
            bool: 删除是否成功
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception:
            return False

    def list_uploaded_files(self) -> List[dict]:
        """
        列出所有上传的文件

        Returns:
            List[dict]: 文件信息列表
        """
        try:
            files = []
            for filename in os.listdir(self.upload_dir):
                file_path = os.path.join(self.upload_dir, filename)
                if os.path.isfile(file_path):
                    file_info = self.get_file_info(file_path)
                    if file_info:
                        files.append(file_info)
            return files
        except Exception:
            return []
