import asyncio
import uuid
import json
from datetime import datetime
from typing import Dict, Any, AsyncGenerator, List
from autogen_agentchat.teams import <PERSON><PERSON><PERSON>h<PERSON><PERSON><PERSON>, GraphFlow

from agents.element_recognition import ElementRecognitionAgent
from agents.interaction_analysis import InteractionAnalysisAgent
# 对于 DeepSeek，使用简化版本避免模型兼容性问题
from simple_agents import SimpleUseCaseGenerationAgent
from models.schemas import (
    AnalysisRequest, AnalysisResult, StreamMessage, MessageType, AgentType,
    ElementInfo, InteractionInfo, UseCaseInfo
)
from utils.config import Config

class AgentService:
    """智能体协调服务类"""

    def __init__(self):
        """初始化智能体服务"""
        self.config = Config()

        # 结果缓存：存储最近的分析结果，避免重复分析
        self._result_cache: Dict[str, AnalysisResult] = {}
        self._cache_max_size = 10  # 最多缓存10个结果

        # 检查 OpenAI API Key 是否有效
        if self.config.OPENAI_API_KEY and self.config.OPENAI_API_KEY != "your_openai_api_key_here":
            # 使用 AutoGen 版本的智能体
            self.element_agent = ElementRecognitionAgent(
                api_key=self.config.OPENAI_API_KEY,
                base_url=self.config.OPENAI_BASE_URL
            )

            self.interaction_agent = InteractionAnalysisAgent(
                api_key=self.config.OPENAI_API_KEY,
                base_url=self.config.OPENAI_BASE_URL
            )

            # AutoGen 版本也需要用例生成智能体
            from simple_agents import SimpleUseCaseGenerationAgent
            self.usecase_agent = SimpleUseCaseGenerationAgent(
                api_key=self.config.DEEPSEEK_API_KEY,
                base_url=self.config.DEEPSEEK_BASE_URL
            )
            self.use_autogen = True
        else:
            # 回退到简化版智能体
            from simple_agents import SimpleElementRecognitionAgent, SimpleInteractionAnalysisAgent, SimpleUseCaseGenerationAgent
            self.element_agent = SimpleElementRecognitionAgent(
                api_key=self.config.DEEPSEEK_API_KEY,
                base_url=self.config.DEEPSEEK_BASE_URL
            )

            self.interaction_agent = SimpleInteractionAnalysisAgent(
                api_key=self.config.DEEPSEEK_API_KEY,
                base_url=self.config.DEEPSEEK_BASE_URL
            )

            self.usecase_agent = SimpleUseCaseGenerationAgent(
                api_key=self.config.DEEPSEEK_API_KEY,
                base_url=self.config.DEEPSEEK_BASE_URL
            )
            self.use_autogen = False
            print("⚠️  OpenAI API Key 未配置，使用简化版智能体")

        # 由于使用了混合智能体（AutoGen + 简化版），暂时不使用 GraphFlow
        # 改为顺序执行
        pass
    
    async def analyze_image_stream(self, request: AnalysisRequest) -> AsyncGenerator[StreamMessage, None]:
        """
        流式分析图片

        Args:
            request: 分析请求对象

        Yields:
            StreamMessage: 流式消息对象
        """
        request_id = str(uuid.uuid4())
        start_time = datetime.now()

        try:
            # 发送开始消息
            yield StreamMessage(
                id=str(uuid.uuid4()),
                type=MessageType.SYSTEM_INFO,
                content="开始图片分析流程...",
                timestamp=datetime.now(),
                metadata={"request_id": request_id}
            )
            
            # 步骤1：元素识别
            yield StreamMessage(
                id=str(uuid.uuid4()),
                type=MessageType.AGENT_PROCESSING,
                agent=AgentType.ELEMENT_RECOGNITION,
                content="正在识别UI元素...",
                timestamp=datetime.now()
            )

            elements_result = await self.element_agent.analyze_image(
                request.image_path,
                request.user_message or ""
            )

            yield StreamMessage(
                id=str(uuid.uuid4()),
                type=MessageType.AGENT_RESULT,
                agent=AgentType.ELEMENT_RECOGNITION,
                content=f"识别到 {len(elements_result.get('elements', []))} 个UI元素",
                timestamp=datetime.now(),
                metadata={"elements_count": len(elements_result.get('elements', []))}
            )

            # 步骤2：交互分析
            yield StreamMessage(
                id=str(uuid.uuid4()),
                type=MessageType.AGENT_PROCESSING,
                agent=AgentType.INTERACTION_ANALYSIS,
                content="正在分析交互行为...",
                timestamp=datetime.now()
            )

            interactions_result = await self.interaction_agent.analyze_interactions(
                elements_result,
                request.user_message or ""
            )

            yield StreamMessage(
                id=str(uuid.uuid4()),
                type=MessageType.AGENT_RESULT,
                agent=AgentType.INTERACTION_ANALYSIS,
                content=f"分析了 {len(interactions_result.get('interactions', []))} 种交互行为",
                timestamp=datetime.now(),
                metadata={"interactions_count": len(interactions_result.get('interactions', []))}
            )

            # 步骤3：用例生成
            yield StreamMessage(
                id=str(uuid.uuid4()),
                type=MessageType.AGENT_PROCESSING,
                agent=AgentType.USECASE_GENERATION,
                content="正在生成测试用例...",
                timestamp=datetime.now()
            )

            usecases_result = await self.usecase_agent.generate_use_cases(
                elements_result,
                interactions_result,
                request.user_message or ""
            )

            yield StreamMessage(
                id=str(uuid.uuid4()),
                type=MessageType.AGENT_RESULT,
                agent=AgentType.USECASE_GENERATION,
                content=f"生成了 {len(usecases_result.get('use_cases', []))} 个测试用例",
                timestamp=datetime.now(),
                metadata={"use_cases_count": len(usecases_result.get('use_cases', []))}
            )

            # 构建完整结果并缓存
            processing_time = (datetime.now() - start_time).total_seconds()

            # 解析结果
            elements = self._parse_elements(elements_result.get('elements', []))
            interactions = self._parse_interactions(interactions_result.get('interactions', []))
            use_cases = self._parse_use_cases(usecases_result.get('use_cases', []))

            # 生成分析总结
            summary = self._generate_summary(elements, interactions, use_cases)

            # 创建完整结果
            complete_result = AnalysisResult(
                request_id=request_id,
                image_path=request.image_path,
                elements=elements,
                interactions=interactions,
                use_cases=use_cases,
                analysis_summary=summary,
                processing_time=processing_time
            )

            # 缓存结果
            cache_key = f"{request.image_path}:{request.user_message or ''}"
            self._cache_result(cache_key, complete_result)

            # 发送完成消息
            yield StreamMessage(
                id=str(uuid.uuid4()),
                type=MessageType.COMPLETE,
                content=f"分析完成！总耗时：{processing_time:.2f}秒",
                timestamp=datetime.now(),
                metadata={
                    "request_id": request_id,
                    "processing_time": processing_time
                }
            )
            
        except Exception as e:
            yield StreamMessage(
                id=str(uuid.uuid4()),
                type=MessageType.ERROR,
                content=f"分析过程中发生错误：{str(e)}",
                timestamp=datetime.now(),
                metadata={"request_id": request_id, "error": str(e)}
            )
    
    async def analyze_image(self, request: AnalysisRequest) -> AnalysisResult:
        """
        完整分析图片（非流式）

        Args:
            request: 分析请求对象

        Returns:
            AnalysisResult: 完整的分析结果

        Raises:
            Exception: 分析失败时抛出异常
        """
        # 首先检查缓存
        cache_key = f"{request.image_path}:{request.user_message or ''}"
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            print(f"返回缓存的分析结果: {cache_key}")
            return cached_result

        request_id = str(uuid.uuid4())
        start_time = datetime.now()

        try:
            # 1. 元素识别
            elements_result = await self.element_agent.analyze_image(
                request.image_path,
                request.user_message or ""
            )

            # 2. 交互分析
            interactions_result = await self.interaction_agent.analyze_interactions(
                elements_result,
                request.user_message or ""
            )

            # 3. 用例生成
            usecases_result = await self.usecase_agent.generate_use_cases(
                elements_result,
                interactions_result,
                request.user_message or ""
            )
            
            # 解析结果
            elements = self._parse_elements(elements_result.get('elements', []))
            interactions = self._parse_interactions(interactions_result.get('interactions', []))
            use_cases = self._parse_use_cases(usecases_result.get('use_cases', []))
            
            # 生成分析总结
            summary = self._generate_summary(elements, interactions, use_cases)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return AnalysisResult(
                request_id=request_id,
                image_path=request.image_path,
                elements=elements,
                interactions=interactions,
                use_cases=use_cases,
                analysis_summary=summary,
                processing_time=processing_time
            )
            
        except Exception as e:
            raise Exception(f"图片分析失败：{str(e)}")
    
    def _build_analysis_task(self, request: AnalysisRequest) -> str:
        """
        构建分析任务描述

        Args:
            request: 分析请求对象

        Returns:
            str: 任务描述字符串
        """
        task = f"""请分析图片：{request.image_path}

用户描述：{request.user_message or '无'}

请按照以下流程进行分析：
1. 首先识别图片中的所有UI元素
2. 然后分析可能的用户交互行为
3. 最后生成相应的测试用例

请提供详细的分析结果。"""

        return task
    
    def _parse_elements(self, elements_data: list) -> List[ElementInfo]:
        """
        解析元素数据

        Args:
            elements_data: 原始元素数据列表

        Returns:
            List[ElementInfo]: 解析后的元素信息列表
        """
        elements = []
        for item in elements_data:
            try:
                element = ElementInfo(
                    element_type=item.get('element_type', 'unknown'),
                    position=item.get('position', {}),
                    size=item.get('size', {}),
                    text=item.get('text'),
                    attributes=item.get('attributes', {}),
                    confidence=item.get('confidence', 0.0)
                )
                elements.append(element)
            except Exception:
                continue
        return elements
    
    def _parse_interactions(self, interactions_data: list) -> List[InteractionInfo]:
        """
        解析交互数据

        Args:
            interactions_data: 原始交互数据列表

        Returns:
            List[InteractionInfo]: 解析后的交互信息列表
        """
        interactions = []
        for item in interactions_data:
            try:
                interaction = InteractionInfo(
                    interaction_type=item.get('interaction_type', 'unknown'),
                    target_element=item.get('target_element', ''),
                    description=item.get('description', ''),
                    sequence=item.get('sequence', 0),
                    conditions=item.get('conditions', [])
                )
                interactions.append(interaction)
            except Exception:
                continue
        return interactions

    def _parse_use_cases(self, usecases_data: list) -> List[UseCaseInfo]:
        """
        解析用例数据

        Args:
            usecases_data: 原始用例数据列表

        Returns:
            List[UseCaseInfo]: 解析后的用例信息列表
        """
        use_cases = []
        for item in usecases_data:
            try:
                use_case = UseCaseInfo(
                    title=item.get('title', ''),
                    description=item.get('description', ''),
                    steps=item.get('steps', []),
                    preconditions=item.get('preconditions', []),
                    expected_results=item.get('expected_results', []),
                    priority=item.get('priority', 'medium')
                )
                use_cases.append(use_case)
            except Exception:
                continue
        return use_cases
    
    def _generate_summary(self, elements: List[ElementInfo], interactions: List[InteractionInfo], use_cases: List[UseCaseInfo]) -> str:
        """
        生成分析总结

        Args:
            elements: 元素信息列表
            interactions: 交互信息列表
            use_cases: 用例信息列表

        Returns:
            str: 分析总结文本
        """
        summary = f"""
分析总结：
- 识别到 {len(elements)} 个UI元素
- 分析了 {len(interactions)} 种交互行为
- 生成了 {len(use_cases)} 个测试用例

主要发现：
"""

        if elements:
            element_types = list(set([e.element_type for e in elements]))
            summary += f"- UI元素类型：{', '.join(element_types)}\n"

        if interactions:
            interaction_types = list(set([i.interaction_type for i in interactions]))
            summary += f"- 交互类型：{', '.join(interaction_types)}\n"

        if use_cases:
            priorities = [uc.priority for uc in use_cases]
            high_priority = priorities.count('high')
            summary += f"- 高优先级用例：{high_priority} 个\n"

        return summary.strip()

    def _cache_result(self, cache_key: str, result: AnalysisResult):
        """缓存分析结果"""
        # 如果缓存已满，删除最旧的结果
        if len(self._result_cache) >= self._cache_max_size:
            oldest_key = next(iter(self._result_cache))
            del self._result_cache[oldest_key]

        self._result_cache[cache_key] = result
        print(f"缓存分析结果: {cache_key}")

    def _get_cached_result(self, cache_key: str) -> AnalysisResult | None:
        """获取缓存的分析结果"""
        return self._result_cache.get(cache_key)

    def clear_cache(self):
        """清空结果缓存"""
        self._result_cache.clear()
        print("结果缓存已清空")

    async def close(self):
        """关闭所有智能体连接"""
        await self.element_agent.close()
        await self.interaction_agent.close()
        await self.usecase_agent.close()
