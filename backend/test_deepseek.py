#!/usr/bin/env python3
"""
测试 DeepSeek API 连接
"""
import asyncio
from openai import Async<PERSON>penAI
from utils.config import Config

async def test_deepseek():
    """测试 DeepSeek API"""
    config = Config()
    
    print(f"API Key: {config.DEEPSEEK_API_KEY[:10]}...")
    print(f"Base URL: {config.DEEPSEEK_BASE_URL}")
    
    client = AsyncOpenAI(
        api_key=config.DEEPSEEK_API_KEY,
        base_url=config.DEEPSEEK_BASE_URL
    )
    
    try:
        response = await client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "user", "content": "你好，请回复一个简单的JSON格式：{\"status\": \"success\", \"message\": \"测试成功\"}"}
            ],
            max_tokens=100,
            temperature=0.1
        )
        
        content = response.choices[0].message.content
        print(f"✅ API 调用成功: {content}")
        
    except Exception as e:
        print(f"❌ API 调用失败: {e}")
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(test_deepseek())
