#!/usr/bin/env python3
"""
测试导入是否正常
"""

def test_imports():
    """测试所有模块的导入"""
    print("🧪 测试模块导入...")
    
    try:
        print("  测试基础模块...")
        import os
        import sys
        from pathlib import Path
        print("  ✅ 基础模块")
        
        print("  测试配置模块...")
        from utils.config import Config
        print("  ✅ 配置模块")
        
        print("  测试数据模型...")
        from models.schemas import (
            AnalysisRequest, AnalysisResult, StreamMessage, 
            MessageType, AgentType, ElementInfo, InteractionInfo, UseCaseInfo
        )
        print("  ✅ 数据模型")
        
        print("  测试图片服务...")
        from services.image_service import ImageService
        print("  ✅ 图片服务")
        
        print("  测试智能体服务...")
        try:
            from services.agent_service import AgentService
            print("  ✅ AutoGen 智能体服务")
            autogen_available = True
        except ImportError as e:
            print(f"  ⚠️  AutoGen 智能体服务导入失败: {e}")
            try:
                from simple_agent_service import SimpleAgentService
                print("  ✅ 简化版智能体服务")
                autogen_available = False
            except ImportError as e2:
                print(f"  ❌ 简化版智能体服务也导入失败: {e2}")
                return False
        
        print("  测试智能体...")
        try:
            from agents.element_recognition import ElementRecognitionAgent
            from agents.interaction_analysis import InteractionAnalysisAgent
            from agents.usecase_generation import UseCaseGenerationAgent
            print("  ✅ AutoGen 智能体")
        except ImportError as e:
            print(f"  ⚠️  AutoGen 智能体导入失败: {e}")
            try:
                from simple_agents import (
                    SimpleElementRecognitionAgent,
                    SimpleInteractionAnalysisAgent,
                    SimpleUseCaseGenerationAgent
                )
                print("  ✅ 简化版智能体")
            except ImportError as e2:
                print(f"  ❌ 简化版智能体也导入失败: {e2}")
                return False
        
        print("  测试 FastAPI 相关...")
        from fastapi import FastAPI, UploadFile, File, Form, HTTPException
        from fastapi.middleware.cors import CORSMiddleware
        from fastapi.staticfiles import StaticFiles
        from fastapi.responses import StreamingResponse
        print("  ✅ FastAPI 相关")
        
        print("\n🎉 所有模块导入成功！")
        print(f"智能体模式: {'AutoGen' if autogen_available else '简化版'}")
        return True
        
    except Exception as e:
        print(f"\n❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config():
    """测试配置"""
    print("\n🔧 测试配置...")
    try:
        from utils.config import Config
        
        print(f"  上传目录: {Config.UPLOAD_DIR}")
        print(f"  最大文件大小: {Config.MAX_FILE_SIZE}")
        print(f"  允许的扩展名: {Config.ALLOWED_EXTENSIONS}")
        print(f"  主机: {Config.HOST}")
        print(f"  端口: {Config.PORT}")
        
        # 检查 API Keys
        api_keys = {
            "OPENAI_API_KEY": Config.OPENAI_API_KEY,
            "DEEPSEEK_API_KEY": Config.DEEPSEEK_API_KEY,
            "UITARS_API_KEY": Config.UITARS_API_KEY
        }
        
        configured_keys = []
        missing_keys = []
        
        for key, value in api_keys.items():
            if value and not value.startswith("your_"):
                configured_keys.append(key)
            else:
                missing_keys.append(key)
        
        if configured_keys:
            print(f"  ✅ 已配置的 API Keys: {', '.join(configured_keys)}")
        
        if missing_keys:
            print(f"  ⚠️  未配置的 API Keys: {', '.join(missing_keys)}")
            print("  请在 .env 文件中配置这些 API Keys")
        
        return len(configured_keys) > 0
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 导入和配置测试")
    print("="*50)
    
    # 测试导入
    import_success = test_imports()
    
    # 测试配置
    config_success = test_config()
    
    print("\n" + "="*50)
    print("📊 测试结果:")
    print("="*50)
    print(f"模块导入: {'✅ 成功' if import_success else '❌ 失败'}")
    print(f"配置检查: {'✅ 成功' if config_success else '⚠️  部分配置缺失'}")
    
    if import_success:
        print("\n🎉 系统可以启动！")
        if config_success:
            print("💡 运行: python main.py")
        else:
            print("💡 请先配置 .env 文件中的 API Keys，然后运行: python main.py")
    else:
        print("\n❌ 系统无法启动，请检查依赖安装")
        print("💡 运行: python fix_dependencies.py")
    
    return import_success

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 测试被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        exit(1)
