from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from typing import Dict, Any, List
import json
import base64
from PIL import Image
import io

class ElementRecognitionAgent:
    """UI元素识别智能体类"""

    def __init__(self, api_key: str, base_url: str = "https://api.openai.com/v1"):
        """
        初始化元素识别智能体

        Args:
            api_key: OpenAI API密钥
            base_url: API基础URL
        """
        self.model_client = OpenAIChatCompletionClient(
            model="gpt-4o",  # 使用支持视觉的模型
            api_key=api_key,
            base_url=base_url
        )
        
        self.system_message = """你是UI元素识别专家，专门分析Web界面中的可测试交互元素，为UI自动化测试提供精确的元素信息。你的分析将用于MidScene.js自动化测试框架。

## 核心职责

### 1. 仅识别可测试的交互元素
**重点识别以下元素类型（按优先级排序）**:

**1. 按钮类元素**:
- 提交按钮、确认按钮、取消按钮
- 登录按钮、注册按钮、保存按钮
- 编辑按钮、删除按钮、添加按钮
- 搜索按钮、重置按钮、刷新按钮

**2. 输入类元素**:
- 文本输入框、密码输入框、搜索框
- 文本域（textarea）
- 数字输入框、邮箱输入框、电话输入框

**3. 选择类元素**:
- 下拉菜单（select）、多选下拉框
- 复选框（checkbox）、单选按钮（radio）
- 开关切换按钮（toggle/switch）

**4. 链接类元素**:
- 导航链接、菜单链接
- 操作链接（编辑、删除、查看详情等）
- 外部链接、下载链接

**5. 特殊交互元素**:
- 文件上传按钮
- 日期选择器、时间选择器
- 滑块、评分组件

### 忽略的元素类型：
- 纯装饰性图片、图标
- 静态文本、标题、段落
- 布局容器（div、section等）
- 纯展示性的表格、列表
- 面包屑导航、页面标题
- 版权信息、页脚内容

### 2. 元素识别标准
**必须满足以下条件之一才识别**:
- 可以点击执行操作（按钮、链接）
- 可以输入内容（输入框、文本域）
- 可以选择或切换状态（下拉菜单、复选框、单选按钮、开关）
- 可以上传文件或选择日期时间

**元素描述要求**:
- 使用MidScene.js友好的自然语言描述
- 包含位置、颜色、文字内容等关键特征
- 确保描述足够具体，能够唯一定位元素

**位置描述标准**:
- **绝对位置**: "页面左上角"、"右上角"、"中央区域"
- **相对位置**: "搜索框右侧"、"表单底部"、"导航栏中"
- **文字定位**: 优先使用按钮文字、链接文字、标签文字进行定位

### 3. 测试操作适配
**MidScene.js操作映射**:
- **aiTap**: 按钮、链接、复选框、单选按钮、开关
- **aiInput**: 文本输入框、密码框、搜索框、文本域
- **aiQuery**: 获取输入框内容、按钮状态、选择框选项
- **aiAssert**: 验证元素存在、文字内容、状态正确性

## 质量标准与要求

### 精准识别原则
- **只识别可测试元素**: 严格按照上述5类交互元素进行识别
- **避免过度识别**: 不识别纯展示性、装饰性元素
- **优先核心功能**: 登录、提交、搜索等核心业务操作优先

### 描述准确性
- **元素类型准确**: 正确区分button、input、select、link等
- **状态识别准确**: 准确识别启用/禁用、必填/可选状态
- **文本内容完整**: 提取完整的按钮文字、链接文字、标签文字

## 输出格式要求

请严格按照以下JSON格式输出，只包含可测试的交互元素：

{
  "title": "根据页面内容自动生成的页面标题",
  "description": "页面主要功能的简要描述",
  "elements": [
    {
      "id": "element_001",
      "name": "登录按钮",
      "element_type": "button",
      "description": "页面右上角的蓝色登录按钮，白色文字'登录'",
      "text_content": "登录",
      "position": {
        "area": "页面右上角",
        "relative_to": "导航栏右侧"
      },
      "visual_features": {
        "color": "蓝色背景，白色文字",
        "size": "中等尺寸",
        "shape": "圆角矩形"
      },
      "functionality": "用户登录入口",
      "interaction_state": "可点击",
      "testability": {
        "is_testable": true,
        "test_priority": "high",
        "midscene_operations": ["aiTap", "aiAssert"],
        "test_scenarios": ["点击登录", "验证按钮存在"]
      },
      "locator_suggestions": {
        "natural_language": "页面右上角的蓝色登录按钮",
        "alternative_descriptions": [
          "登录按钮",
          "蓝色的登录按钮"
        ]
      },
      "confidence_score": 0.95
    }
  ]
}

## 特别注意事项

1. **严格筛选**: 只识别可点击、可输入、可选择的交互元素
2. **避免冗余**: 不要识别装饰性图片、静态文本、布局容器
3. **优先核心功能**: 登录、提交、搜索等核心操作元素优先
4. **简化描述**: 提供足够定位信息即可，避免过度详细
5. **确保可操作**: 每个识别的元素都必须支持MidScene.js操作

请开始分析工作，只输出可测试的交互元素。

    """

        self.agent = AssistantAgent(
            name="element_recognition_agent",
            model_client=self.model_client,
            system_message=self.system_message,
            model_client_stream=True
        )
    
    def encode_image(self, image_path: str) -> str:
        """
        将图片编码为base64格式

        Args:
            image_path: 图片文件路径

        Returns:
            str: base64编码的图片数据

        Raises:
            Exception: 图片编码失败时抛出异常
        """
        try:
            with Image.open(image_path) as img:
                # 如果图片太大，进行压缩
                if img.width > 1024 or img.height > 1024:
                    img.thumbnail((1024, 1024), Image.Resampling.LANCZOS)

                # 转换为RGB格式（如果需要）
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 保存到内存中
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=85)
                buffer.seek(0)

                # 编码为base64
                image_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
                return f"data:image/jpeg;base64,{image_data}"
        except Exception as e:
            raise Exception(f"图片编码失败: {str(e)}")
    
    async def analyze_image(self, image_path: str, user_message: str = "") -> Dict[str, Any]:
        """
        分析图片中的UI元素

        Args:
            image_path: 图片文件路径
            user_message: 用户附加的描述信息

        Returns:
            Dict[str, Any]: 分析结果，包含元素列表或错误信息
        """
        try:
            # 编码图片
            encoded_image = self.encode_image(image_path)

            # 构建分析任务 - 在 AutoGen 0.6.1 中，我们直接使用文本任务
            task = f"""请分析这张UI界面截图，识别其中的所有UI元素。

用户附加信息：{user_message if user_message else "无"}

请提供详细的元素识别结果，包括元素类型、位置、属性等信息。请以JSON格式返回结果。

图片数据：{encoded_image}"""

            # 运行智能体分析
            print(f"🔍 开始元素识别分析，图片路径: {image_path}")
            result = await self.agent.run(task=task)
            print(f"📝 智能体返回结果: {result}")

            # 解析结果
            if result and result.messages:
                last_message = result.messages[-1]
                content = last_message.content
                print(f"📄 智能体响应内容: {content[:500]}...")  # 只显示前500字符

                # 尝试解析JSON结果
                try:
                    # 查找JSON内容
                    json_start = content.find('{')
                    json_end = content.rfind('}') + 1
                    if json_start != -1 and json_end > json_start:
                        json_content = content[json_start:json_end]
                        print(f"🔧 提取的JSON内容: {json_content[:200]}...")
                        parsed_result = json.loads(json_content)
                        print(f"✅ JSON解析成功，元素数量: {len(parsed_result.get('elements', []))}")
                        return parsed_result
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"🔧 原始内容: {content}")

                # 如果无法解析JSON，返回原始内容
                print("⚠️ 无法解析JSON，返回原始内容")
                return {
                    "elements": [],
                    "raw_analysis": content,
                    "status": "partial_success"
                }

            print("❌ 智能体没有返回有效结果")
            return {
                "elements": [],
                "error": "No analysis result received",
                "status": "failed"
            }

        except Exception as e:
            print(f"💥 元素识别发生异常: {e}")
            import traceback
            print(f"📋 异常详情: {traceback.format_exc()}")
            return {
                "elements": [],
                "error": str(e),
                "status": "failed"
            }
    
    async def run_stream(self, task: str):
        """
        流式运行智能体

        Args:
            task: 分析任务描述

        Yields:
            智能体处理过程中的事件
        """
        async for event in self.agent.run_stream(task=task):
            yield event

    async def close(self):
        """关闭模型客户端连接"""
        await self.model_client.close()
