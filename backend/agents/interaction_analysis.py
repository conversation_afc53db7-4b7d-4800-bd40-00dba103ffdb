from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from typing import Dict, Any, List
import json

class InteractionAnalysisAgent:
    """交互分析智能体类"""

    def __init__(self, api_key: str, base_url: str = "https://api.openai.com/v1"):
        """
        初始化交互分析智能体

        Args:
            api_key: OpenAI API密钥
            base_url: API基础URL
        """
        self.model_client = OpenAIChatCompletionClient(
            model="gpt-4o",
            api_key=api_key,
            base_url=base_url
        )
        
        self.system_message = """你是一个专业的UI交互分析智能体。你的任务是基于UI元素识别结果，分析界面中可能的用户交互行为和交互流程。

请按照以下要求进行分析：

1. **交互类型识别**：识别各种可能的交互类型，包括：
   - 点击 (Click)
   - 双击 (Double Click)
   - 长按 (Long Press)
   - 拖拽 (Drag & Drop)
   - 滑动 (Swipe)
   - 输入 (Input/Type)
   - 选择 (Select)
   - 滚动 (Scroll)
   - 悬停 (Hover)
   - 手势操作 (Gesture)

2. **交互流程分析**：分析用户可能的操作流程和路径：
   - 主要操作流程
   - 替代操作路径
   - 错误处理流程
   - 快捷操作方式

3. **交互关系**：分析元素之间的交互关系：
   - 依赖关系
   - 触发关系
   - 状态变化
   - 数据流向

4. **用户体验分析**：评估交互设计的用户体验：
   - 操作便利性
   - 学习成本
   - 错误预防
   - 反馈机制

5. **输出格式**：以JSON格式输出结果，包含interactions数组，每个交互包含：
   - interaction_type: 交互类型
   - target_element: 目标元素
   - description: 交互描述
   - sequence: 交互序列
   - conditions: 交互条件
   - expected_outcome: 预期结果

请基于提供的UI元素信息进行深入的交互分析。"""

        self.agent = AssistantAgent(
            name="interaction_analysis_agent",
            model_client=self.model_client,
            system_message=self.system_message,
            model_client_stream=True
        )
    
    async def analyze_interactions(self, elements_data: Dict[str, Any], user_message: str = "") -> Dict[str, Any]:
        """
        分析UI交互行为

        Args:
            elements_data: UI元素识别结果数据
            user_message: 用户附加的描述信息

        Returns:
            Dict[str, Any]: 交互分析结果，包含交互列表或错误信息
        """
        try:
            # 构建分析任务
            task = f"""请基于以下UI元素识别结果，分析界面中可能的用户交互行为和交互流程。

UI元素数据：
{json.dumps(elements_data, ensure_ascii=False, indent=2)}

用户附加信息：{user_message if user_message else "无"}

请提供详细的交互分析结果，包括交互类型、操作流程、用户体验评估等。"""

            # 运行智能体分析
            result = await self.agent.run(task=task)

            # 解析结果
            if result and result.messages:
                last_message = result.messages[-1]
                content = last_message.content

                # 尝试解析JSON结果
                try:
                    # 查找JSON内容
                    json_start = content.find('{')
                    json_end = content.rfind('}') + 1
                    if json_start != -1 and json_end > json_start:
                        json_content = content[json_start:json_end]
                        parsed_result = json.loads(json_content)
                        return parsed_result
                except json.JSONDecodeError:
                    pass

                # 如果无法解析JSON，返回原始内容
                return {
                    "interactions": [],
                    "raw_analysis": content,
                    "status": "partial_success"
                }

            return {
                "interactions": [],
                "error": "No analysis result received",
                "status": "failed"
            }

        except Exception as e:
            return {
                "interactions": [],
                "error": str(e),
                "status": "failed"
            }
    
    async def run_stream(self, task: str):
        """
        流式运行智能体

        Args:
            task: 分析任务描述

        Yields:
            智能体处理过程中的事件
        """
        async for event in self.agent.run_stream(task=task):
            yield event

    async def close(self):
        """关闭模型客户端连接"""
        await self.model_client.close()
