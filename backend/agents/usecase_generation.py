from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.models.openai._model_info import ModelInfo
from typing import Dict, Any, List
import json

class UseCaseGenerationAgent:
    """用例生成智能体类"""

    def __init__(self, api_key: str, base_url: str = "https://api.deepseek.com/v1"):
        """
        初始化用例生成智能体

        Args:
            api_key: DeepSeek API密钥
            base_url: API基础URL
        """
        # 创建 DeepSeek 模型信息
        deepseek_model_info = ModelInfo(
            vision=False,
            function_calling=True,
            json_output=True
        )

        self.model_client = OpenAIChatCompletionClient(
            model="deepseek-chat",
            api_key=api_key,
            base_url=base_url,
            model_info=deepseek_model_info
        )
        
        self.system_message = """你是一个专业的测试用例生成智能体。你的任务是基于UI元素识别和交互分析结果，生成全面的测试用例。

请按照以下要求生成测试用例：

1. **功能测试用例**：
   - 正常功能流程测试
   - 边界值测试
   - 异常输入测试
   - 数据验证测试

2. **UI测试用例**：
   - 界面元素显示测试
   - 布局响应式测试
   - 交互反馈测试
   - 视觉一致性测试

3. **用户体验测试用例**：
   - 易用性测试
   - 可访问性测试
   - 性能体验测试
   - 错误处理测试

4. **兼容性测试用例**：
   - 浏览器兼容性
   - 设备兼容性
   - 分辨率适配测试

5. **安全性测试用例**：
   - 输入安全测试
   - 权限控制测试
   - 数据保护测试

6. **用例格式**：每个测试用例包含：
   - title: 用例标题
   - description: 用例描述
   - preconditions: 前置条件
   - steps: 测试步骤
   - expected_results: 预期结果
   - priority: 优先级 (high/medium/low)
   - category: 用例分类

请基于提供的UI分析结果生成详细、可执行的测试用例。"""

        self.agent = AssistantAgent(
            name="usecase_generation_agent",
            model_client=self.model_client,
            system_message=self.system_message,
            model_client_stream=True
        )
    
    async def generate_use_cases(self, elements_data: Dict[str, Any], interactions_data: Dict[str, Any], user_message: str = "") -> Dict[str, Any]:
        """
        生成测试用例

        Args:
            elements_data: UI元素识别结果数据
            interactions_data: 交互分析结果数据
            user_message: 用户附加的描述信息

        Returns:
            Dict[str, Any]: 用例生成结果，包含用例列表或错误信息
        """
        try:
            # 构建分析任务
            task = f"""请基于以下UI分析结果，生成全面的测试用例。

UI元素识别结果：
{json.dumps(elements_data, ensure_ascii=False, indent=2)}

交互分析结果：
{json.dumps(interactions_data, ensure_ascii=False, indent=2)}

用户附加信息：{user_message if user_message else "无"}

请生成详细的测试用例，涵盖功能测试、UI测试、用户体验测试等各个方面。"""

            # 运行智能体分析
            result = await self.agent.run(task=task)

            # 解析结果
            if result and result.messages:
                last_message = result.messages[-1]
                content = last_message.content

                # 尝试解析JSON结果
                try:
                    # 查找JSON内容
                    json_start = content.find('{')
                    json_end = content.rfind('}') + 1
                    if json_start != -1 and json_end > json_start:
                        json_content = content[json_start:json_end]
                        parsed_result = json.loads(json_content)
                        return parsed_result
                except json.JSONDecodeError:
                    pass

                # 如果无法解析JSON，返回原始内容
                return {
                    "use_cases": [],
                    "raw_analysis": content,
                    "status": "partial_success"
                }

            return {
                "use_cases": [],
                "error": "No analysis result received",
                "status": "failed"
            }

        except Exception as e:
            return {
                "use_cases": [],
                "error": str(e),
                "status": "failed"
            }
    
    async def run_stream(self, task: str):
        """
        流式运行智能体

        Args:
            task: 分析任务描述

        Yields:
            智能体处理过程中的事件
        """
        async for event in self.agent.run_stream(task=task):
            yield event

    async def close(self):
        """关闭模型客户端连接"""
        await self.model_client.close()
