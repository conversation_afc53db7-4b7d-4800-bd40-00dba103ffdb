from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class AgentType(str, Enum):
    """智能体类型枚举"""
    ELEMENT_RECOGNITION = "element_recognition"  # 元素识别智能体
    INTERACTION_ANALYSIS = "interaction_analysis"  # 交互分析智能体
    USECASE_GENERATION = "usecase_generation"  # 用例生成智能体

class MessageType(str, Enum):
    """消息类型枚举"""
    USER_INPUT = "user_input"  # 用户输入
    AGENT_PROCESSING = "agent_processing"  # 智能体处理中
    AGENT_RESULT = "agent_result"  # 智能体结果
    SYSTEM_INFO = "system_info"  # 系统信息
    ERROR = "error"  # 错误信息
    COMPLETE = "complete"  # 完成

class AnalysisRequest(BaseModel):
    """分析请求模型"""
    image_path: str = Field(..., description="上传图片的路径")
    user_message: Optional[str] = Field(None, description="用户附加的文字描述")
    analysis_options: Optional[Dict[str, Any]] = Field(default_factory=dict, description="分析选项")

class StreamMessage(BaseModel):
    """流式消息模型"""
    id: str = Field(..., description="消息ID")
    type: MessageType = Field(..., description="消息类型")
    agent: Optional[AgentType] = Field(None, description="发送消息的智能体")
    content: str = Field(..., description="消息内容")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="元数据")

class ElementInfo(BaseModel):
    """UI元素信息模型"""
    element_type: str = Field(..., description="元素类型")
    position: Dict[str, int] = Field(..., description="元素位置坐标")
    size: Dict[str, int] = Field(..., description="元素大小")
    text: Optional[str] = Field(None, description="元素文本内容")
    attributes: Optional[Dict[str, Any]] = Field(default_factory=dict, description="元素属性")
    confidence: float = Field(..., description="识别置信度")

class InteractionInfo(BaseModel):
    """交互信息模型"""
    interaction_type: str = Field(..., description="交互类型")
    target_element: str = Field(..., description="目标元素")
    description: str = Field(..., description="交互描述")
    sequence: int = Field(..., description="交互序列")
    conditions: Optional[List[str]] = Field(default_factory=list, description="交互条件")

class UseCaseInfo(BaseModel):
    """用例信息模型"""
    title: str = Field(..., description="用例标题")
    description: str = Field(..., description="用例描述")
    steps: List[str] = Field(..., description="用例步骤")
    preconditions: Optional[List[str]] = Field(default_factory=list, description="前置条件")
    expected_results: Optional[List[str]] = Field(default_factory=list, description="预期结果")
    priority: Optional[str] = Field("medium", description="优先级")

class AnalysisResult(BaseModel):
    """分析结果模型"""
    request_id: str = Field(..., description="请求ID")
    image_path: str = Field(..., description="图片路径")
    elements: List[ElementInfo] = Field(..., description="识别的UI元素")
    interactions: List[InteractionInfo] = Field(..., description="交互分析结果")
    use_cases: List[UseCaseInfo] = Field(..., description="生成的用例")
    analysis_summary: str = Field(..., description="分析总结")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    processing_time: Optional[float] = Field(None, description="处理时间(秒)")

class UploadResponse(BaseModel):
    """文件上传响应模型"""
    success: bool = Field(..., description="上传是否成功")
    file_path: Optional[str] = Field(None, description="文件路径")
    file_size: Optional[int] = Field(None, description="文件大小")
    message: str = Field(..., description="响应消息")

class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: bool = Field(True, description="是否为错误")
    message: str = Field(..., description="错误消息")
    code: Optional[str] = Field(None, description="错误代码")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
