#!/usr/bin/env python3
"""
解决依赖冲突问题的安装脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, cwd=None):
    """运行命令并返回结果"""
    try:
        print(f"🔧 执行: {command}")
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            text=True,
            check=True
        )
        print("✅ 成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失败: {e}")
        return False

def clean_environment():
    """清理环境"""
    print("🧹 清理现有包...")
    
    packages_to_remove = [
        "autogen-agentchat",
        "autogen-ext", 
        "pillow",
        "pydantic"
    ]
    
    for package in packages_to_remove:
        print(f"  卸载 {package}...")
        run_command(f"{sys.executable} -m pip uninstall -y {package}")

def install_step_by_step():
    """分步安装，避免冲突"""
    print("\n📦 分步安装依赖...")
    
    # 步骤1：安装基础包
    print("\n步骤1: 安装基础包")
    basic_packages = [
        "python-dotenv==1.0.0",
        "aiofiles==23.2.1", 
        "httpx==0.25.2",
        "requests==2.31.0"
    ]
    
    for package in basic_packages:
        if not run_command(f"{sys.executable} -m pip install {package}"):
            print(f"⚠️  {package} 安装失败，继续...")
    
    # 步骤2：安装 Pillow（让 pip 选择兼容版本）
    print("\n步骤2: 安装 Pillow")
    pillow_commands = [
        f"{sys.executable} -m pip install 'pillow>=9.0.0,<11.0.0'",
        f"{sys.executable} -m pip install pillow",
        f"{sys.executable} -m pip install 'pillow==9.5.0'"
    ]
    
    pillow_installed = False
    for cmd in pillow_commands:
        if run_command(cmd):
            pillow_installed = True
            break
    
    if not pillow_installed:
        print("❌ Pillow 安装失败")
        return False
    
    # 步骤3：安装 Pydantic
    print("\n步骤3: 安装 Pydantic")
    pydantic_commands = [
        f"{sys.executable} -m pip install 'pydantic>=2.0.0,<3.0.0'",
        f"{sys.executable} -m pip install pydantic==2.5.0",
        f"{sys.executable} -m pip install pydantic"
    ]
    
    for cmd in pydantic_commands:
        if run_command(cmd):
            break
    
    # 步骤4：安装 FastAPI 相关
    print("\n步骤4: 安装 FastAPI")
    fastapi_packages = [
        "fastapi==0.104.1",
        "uvicorn[standard]==0.24.0",
        "python-multipart==0.0.6"
    ]
    
    for package in fastapi_packages:
        run_command(f"{sys.executable} -m pip install {package}")
    
    # 步骤5：尝试安装 AutoGen
    print("\n步骤5: 安装 AutoGen")
    autogen_commands = [
        # 方法1：不指定版本，让 pip 解决依赖
        f"{sys.executable} -m pip install autogen-agentchat autogen-ext[openai]",
        # 方法2：指定版本
        f"{sys.executable} -m pip install autogen-agentchat==0.6.1 autogen-ext[openai]==0.6.1",
        # 方法3：预发布版本
        f"{sys.executable} -m pip install --pre autogen-agentchat autogen-ext[openai]",
        # 方法4：从源码安装
        f"{sys.executable} -m pip install git+https://github.com/microsoft/autogen.git#subdirectory=python/packages/autogen-agentchat",
    ]
    
    autogen_installed = False
    for i, cmd in enumerate(autogen_commands, 1):
        print(f"\n  尝试方法 {i}...")
        if run_command(cmd):
            autogen_installed = True
            print(f"✅ AutoGen 安装成功 (方法 {i})")
            break
        else:
            print(f"❌ 方法 {i} 失败")
    
    return autogen_installed

def install_with_constraints():
    """使用约束文件安装"""
    print("\n📋 创建约束文件...")
    
    constraints_content = """# 约束文件 - 解决依赖冲突
pillow>=9.0.0,<11.0.0
pydantic>=2.0.0,<3.0.0
fastapi>=0.100.0,<0.110.0
uvicorn>=0.20.0,<0.30.0
httpx>=0.25.0,<0.30.0
openai>=1.0.0,<2.0.0
"""
    
    constraints_file = Path("backend/constraints.txt")
    with open(constraints_file, 'w') as f:
        f.write(constraints_content)
    
    print("✅ 约束文件已创建")
    
    # 使用约束文件安装
    print("\n📦 使用约束文件安装...")
    
    packages = [
        "python-dotenv",
        "aiofiles", 
        "requests",
        "pillow",
        "pydantic",
        "fastapi",
        "uvicorn[standard]",
        "python-multipart",
        "httpx",
        "openai",
        "autogen-agentchat",
        "autogen-ext[openai]"
    ]
    
    for package in packages:
        cmd = f"{sys.executable} -m pip install -c {constraints_file} {package}"
        if not run_command(cmd):
            print(f"⚠️  {package} 安装失败，继续...")

def create_minimal_requirements():
    """创建最小化的 requirements.txt"""
    print("\n📝 创建最小化依赖文件...")
    
    minimal_content = """# 最小化依赖 - 避免版本冲突
fastapi
uvicorn[standard]
python-multipart
python-dotenv
pillow
aiofiles
pydantic
httpx
openai
requests

# AutoGen (可选 - 如果安装失败可以注释掉)
autogen-agentchat
autogen-ext[openai]
"""
    
    minimal_file = Path("backend/requirements-minimal.txt")
    with open(minimal_file, 'w') as f:
        f.write(minimal_content)
    
    print("✅ 最小化依赖文件已创建: backend/requirements-minimal.txt")
    
    # 尝试安装最小化依赖
    print("\n📦 安装最小化依赖...")
    return run_command(f"{sys.executable} -m pip install -r {minimal_file}")

def test_installation():
    """测试安装结果"""
    print("\n🧪 测试安装结果...")
    
    # 测试基础包
    basic_imports = [
        ("fastapi", "FastAPI"),
        ("uvicorn", "uvicorn"),
        ("PIL", "Pillow"),
        ("pydantic", "Pydantic"),
        ("openai", "OpenAI"),
        ("httpx", "HTTPX"),
        ("aiofiles", "aiofiles"),
        ("dotenv", "python-dotenv")
    ]
    
    failed_imports = []
    
    for module, name in basic_imports:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name}")
            failed_imports.append(name)
    
    # 测试 AutoGen
    try:
        import autogen_agentchat
        import autogen_ext
        print("✅ AutoGen")
        autogen_available = True
    except ImportError:
        print("❌ AutoGen")
        failed_imports.append("AutoGen")
        autogen_available = False
    
    return len(failed_imports) == 0, autogen_available

def main():
    """主函数"""
    print("🔧 依赖冲突解决脚本")
    print("="*50)
    
    # 检查当前目录
    if not Path("backend").exists():
        print("❌ 请在项目根目录运行此脚本")
        return False
    
    print("选择解决方案:")
    print("1. 清理环境并分步安装 (推荐)")
    print("2. 使用约束文件安装")
    print("3. 安装最小化依赖")
    print("4. 全部尝试")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        clean_environment()
        success = install_step_by_step()
    elif choice == "2":
        success = install_with_constraints()
    elif choice == "3":
        success = create_minimal_requirements()
    elif choice == "4":
        print("\n🔄 尝试所有方法...")
        clean_environment()
        success = install_step_by_step()
        if not success:
            success = install_with_constraints()
        if not success:
            success = create_minimal_requirements()
    else:
        print("❌ 无效选择")
        return False
    
    # 测试安装结果
    print("\n" + "="*50)
    all_success, autogen_available = test_installation()
    
    if all_success:
        print("🎉 所有依赖安装成功！")
        print("\n🚀 下一步:")
        print("1. 配置 backend/.env 文件")
        print("2. 运行: cd backend && python main.py")
    elif autogen_available:
        print("✅ 基础依赖和 AutoGen 安装成功！")
        print("\n🚀 下一步:")
        print("1. 配置 backend/.env 文件") 
        print("2. 运行: cd backend && python main.py")
    else:
        print("⚠️  基础依赖安装成功，但 AutoGen 安装失败")
        print("\n🔄 可以尝试:")
        print("1. 使用简化版本: cd backend && python simple_main.py")
        print("2. 手动安装 AutoGen: pip install autogen-agentchat")
        print("3. 或者继续使用基础功能")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装过程中发生错误: {e}")
        print("\n🔧 手动解决方案:")
        print("1. pip install --upgrade pip")
        print("2. pip install fastapi uvicorn python-multipart python-dotenv")
        print("3. pip install pillow pydantic httpx openai requests aiofiles")
        print("4. pip install autogen-agentchat autogen-ext[openai]")
