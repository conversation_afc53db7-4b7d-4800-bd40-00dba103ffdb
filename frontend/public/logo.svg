<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1890ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#722ed1;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="50" cy="50" r="45" fill="url(#grad1)" stroke="#fff" stroke-width="2"/>
  
  <!-- 图片图标 -->
  <rect x="25" y="30" width="50" height="35" rx="3" fill="#fff" opacity="0.9"/>
  <rect x="28" y="33" width="44" height="29" rx="2" fill="#f0f0f0"/>
  
  <!-- 山峰图标 -->
  <polygon points="30,55 38,45 46,50 54,42 62,48 70,55" fill="#1890ff" opacity="0.7"/>
  
  <!-- 太阳 -->
  <circle cx="60" cy="40" r="4" fill="#faad14"/>
  
  <!-- AI 智能体图标 -->
  <circle cx="75" cy="75" r="12" fill="#52c41a" stroke="#fff" stroke-width="2"/>
  <circle cx="72" cy="72" r="2" fill="#fff"/>
  <circle cx="78" cy="72" r="2" fill="#fff"/>
  <path d="M 70 78 Q 75 82 80 78" stroke="#fff" stroke-width="2" fill="none" stroke-linecap="round"/>
</svg>
