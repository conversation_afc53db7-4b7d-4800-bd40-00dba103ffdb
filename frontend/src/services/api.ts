import axios from 'axios';
import { UploadResponse, AnalysisResult, FileInfo } from '../types';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 300000, // 5分钟超时
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API请求:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('API请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API响应:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('API响应错误:', error.response?.status, error.response?.data);
    return Promise.reject(error);
  }
);

/**
 * API服务类
 * 提供与后端API交互的方法
 */
export class ApiService {
  /**
   * 健康检查
   * @returns Promise<any> 健康状态数据
   */
  static async healthCheck() {
    const response = await api.get('/health');
    return response.data;
  }

  /**
   * 上传图片
   * @param file 要上传的图片文件
   * @returns Promise<UploadResponse> 上传结果
   */
  static async uploadImage(file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  /**
   * 分析图片（非流式）
   * @param imagePath 图片文件路径
   * @param userMessage 用户附加描述
   * @returns Promise<AnalysisResult> 分析结果
   */
  static async analyzeImage(imagePath: string, userMessage?: string): Promise<AnalysisResult> {
    const formData = new FormData();
    formData.append('image_path', imagePath);
    if (userMessage) {
      formData.append('user_message', userMessage);
    }

    const response = await api.post('/analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  /**
   * 创建流式分析连接（SSE）
   * @param imagePath 图片文件路径
   * @param userMessage 用户附加描述
   * @returns EventSource SSE连接对象
   */
  static createAnalysisStream(imagePath: string, userMessage?: string): EventSource {
    const formData = new FormData();
    formData.append('image_path', imagePath);
    if (userMessage) {
      formData.append('user_message', userMessage);
    }

    // 构建URL参数
    const params = new URLSearchParams();
    params.append('image_path', imagePath);
    if (userMessage) {
      params.append('user_message', userMessage);
    }

    const url = `${api.defaults.baseURL}/analyze/stream?${params.toString()}`;
    return new EventSource(url);
  }

  /**
   * 获取文件列表
   * @returns Promise<{ files: FileInfo[] }> 文件列表数据
   */
  static async getFiles(): Promise<{ files: FileInfo[] }> {
    const response = await api.get('/files');
    return response.data;
  }

  /**
   * 删除文件
   * @param filePath 要删除的文件路径
   * @returns Promise<{ message: string }> 删除结果消息
   */
  static async deleteFile(filePath: string): Promise<{ message: string }> {
    const response = await api.delete(`/files/${encodeURIComponent(filePath)}`);
    return response.data;
  }

  /**
   * 获取图片访问URL
   * @param imagePath 图片文件路径
   * @returns string 图片的完整访问URL
   */
  static getImageUrl(imagePath: string): string {
    return `${api.defaults.baseURL}/${imagePath}`;
  }
}

export default api;
