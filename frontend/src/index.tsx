import React from 'react';
import ReactDOM from 'react-dom/client';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import 'antd/dist/reset.css';
import App from './App';

// 自定义主题配置
const theme = {
  token: {
    colorPrimary: '#1890ff', // 主色调
    colorSuccess: '#52c41a', // 成功色
    colorWarning: '#faad14', // 警告色
    colorError: '#ff4d4f', // 错误色
    colorInfo: '#1890ff', // 信息色
    borderRadius: 8, // 圆角大小
    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif", // 字体
  },
  components: {
    Card: {
      borderRadius: 12, // 卡片圆角
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)', // 卡片阴影
    },
    Button: {
      borderRadius: 8, // 按钮圆角
    },
    Input: {
      borderRadius: 8, // 输入框圆角
    },
    Table: {
      borderRadius: 8, // 表格圆角
    },
  },
};

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <ConfigProvider 
      locale={zhCN}
      theme={theme}
    >
      <App />
    </ConfigProvider>
  </React.StrictMode>
);
