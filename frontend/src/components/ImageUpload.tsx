import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { InboxOutlined, LoadingOutlined } from '@ant-design/icons';
import { message, Card, Typography, Progress } from 'antd';
import styled from 'styled-components';
import { ApiService } from '../services/api';
import { UploadResponse } from '../types';

const { Text, Title } = Typography;

const UploadContainer = styled.div`
  margin-bottom: 24px;
`;

const DropzoneArea = styled.div<{ $isDragActive: boolean; $hasFile: boolean }>`
  border: 2px dashed ${props => props.$isDragActive ? '#1890ff' : props.$hasFile ? '#52c41a' : '#d9d9d9'};
  border-radius: 12px;
  padding: 48px 24px;
  text-align: center;
  background: ${props => props.$isDragActive ? 'rgba(24, 144, 255, 0.05)' : props.$hasFile ? 'rgba(82, 196, 26, 0.05)' : 'rgba(0, 0, 0, 0.02)'};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    background: rgba(24, 144, 255, 0.05);
    transform: scale(1.02);
  }
`;

const UploadIcon = styled.div`
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 16px;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
`;

const PreviewContainer = styled.div`
  margin-top: 16px;
  text-align: center;
`;

const PreviewImage = styled.img`
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
`;

interface ImageUploadProps {
  onUploadSuccess: (response: UploadResponse) => void;
  onUploadError: (error: string) => void;
}

const ImageUpload: React.FC<ImageUploadProps> = ({ onUploadSuccess, onUploadError }) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFile, setUploadedFile] = useState<UploadResponse | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      message.error('不支持的文件格式！请上传 JPG、PNG、GIF、BMP 或 WebP 格式的图片。');
      return;
    }

    // 检查文件大小（10MB）
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      message.error('文件大小不能超过 10MB！');
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    // 创建预览URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);

      // 上传文件
      const response = await ApiService.uploadImage(file);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      setTimeout(() => {
        setUploading(false);
        setUploadedFile(response);
        onUploadSuccess(response);
        message.success('图片上传成功！');
      }, 500);

    } catch (error: any) {
      setUploading(false);
      setUploadProgress(0);
      setPreviewUrl(null);
      const errorMessage = error.response?.data?.detail || error.message || '上传失败';
      onUploadError(errorMessage);
      message.error(`上传失败：${errorMessage}`);
    }
  }, [onUploadSuccess, onUploadError]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.bmp', '.webp']
    },
    multiple: false,
    disabled: uploading
  });

  return (
    <UploadContainer>
      <Card>
        <Title level={4} style={{ marginBottom: 16, textAlign: 'center' }}>
          📸 上传图片
        </Title>
        
        <DropzoneArea
          {...getRootProps()}
          $isDragActive={isDragActive}
          $hasFile={!!uploadedFile}
        >
          <input {...getInputProps()} />
          
          <UploadIcon>
            {uploading ? <LoadingOutlined spin /> : <InboxOutlined />}
          </UploadIcon>
          
          {uploading ? (
            <div>
              <Text strong>正在上传...</Text>
              <Progress 
                percent={uploadProgress} 
                size="small" 
                style={{ marginTop: 8, maxWidth: 200, margin: '8px auto 0' }} 
              />
            </div>
          ) : isDragActive ? (
            <div>
              <Text strong style={{ color: '#1890ff' }}>松开鼠标上传图片</Text>
              <br />
              <Text type="secondary">支持 JPG、PNG、GIF、BMP、WebP 格式</Text>
            </div>
          ) : (
            <div>
              <Text strong>点击或拖拽图片到此区域上传</Text>
              <br />
              <Text type="secondary">支持 JPG、PNG、GIF、BMP、WebP 格式，最大 10MB</Text>
            </div>
          )}
        </DropzoneArea>

        {previewUrl && (
          <PreviewContainer>
            <PreviewImage
              src={previewUrl}
              alt="预览图片"
            />
            {uploadedFile && (
              <div style={{ marginTop: 8 }}>
                <Text type="success" strong>
                  ✅ 上传成功
                </Text>
                <br />
                <Text type="secondary">
                  文件大小: {(uploadedFile.file_size! / 1024).toFixed(1)} KB
                </Text>
              </div>
            )}
          </PreviewContainer>
        )}
      </Card>
    </UploadContainer>
  );
};

export default ImageUpload;
