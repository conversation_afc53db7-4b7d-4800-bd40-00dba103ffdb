import React, { useState } from 'react';
import { Card, Tabs, Table, Tag, Typography, Space, Progress, Statistic, Row, Col, Collapse, Badge } from 'antd';
import { BugOutlined, InteractionOutlined, FileTextOutlined, TrophyOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { AnalysisResult, ElementInfo, InteractionInfo, UseCaseInfo } from '../types';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;

const ResultsContainer = styled(motion.div)`
  margin-top: 24px;
`;

const StatCard = styled(Card)`
  text-align: center;
  .ant-statistic-content {
    color: #1890ff;
  }
`;

const ElementCard = styled(Card)`
  margin-bottom: 16px;
  border-left: 4px solid #52c41a;
`;

const InteractionCard = styled(Card)`
  margin-bottom: 16px;
  border-left: 4px solid #1890ff;
`;

const UseCaseCard = styled(Card)`
  margin-bottom: 16px;
  border-left: 4px solid #722ed1;
`;

const ConfidenceBar = styled.div<{ confidence: number }>`
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  
  &::after {
    content: '';
    display: block;
    width: ${props => props.confidence * 100}%;
    height: 100%;
    background: ${props => 
      props.confidence >= 0.8 ? '#52c41a' :
      props.confidence >= 0.6 ? '#faad14' : '#ff4d4f'
    };
    transition: width 0.3s ease;
  }
`;

/**
 * 分析结果组件的属性接口
 */
interface AnalysisResultsProps {
  result: AnalysisResult | null; // 分析结果数据
  loading?: boolean; // 是否正在加载
}

/**
 * 分析结果展示组件
 * 以多标签页形式展示分析结果
 */
const AnalysisResults: React.FC<AnalysisResultsProps> = ({ result, loading = false }) => {
  const [activeTab, setActiveTab] = useState('overview');

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Progress type="circle" percent={75} />
          <div style={{ marginTop: 16 }}>
            <Text>正在分析图片，请稍候...</Text>
          </div>
        </div>
      </Card>
    );
  }

  if (!result) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#8c8c8c' }}>
          <FileTextOutlined style={{ fontSize: 48, marginBottom: 16 }} />
          <div>
            <Text type="secondary">暂无分析结果</Text>
          </div>
        </div>
      </Card>
    );
  }

  const elementColumns = [
    {
      title: '元素类型',
      dataIndex: 'element_type',
      key: 'element_type',
      render: (type: string) => <Tag color="green">{type}</Tag>
    },
    {
      title: '文本内容',
      dataIndex: 'text',
      key: 'text',
      render: (text: string) => text || <Text type="secondary">无</Text>
    },
    {
      title: '置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      render: (confidence: number) => (
        <div>
          <ConfidenceBar confidence={confidence} />
          <Text style={{ fontSize: '12px', marginTop: 4 }}>
            {(confidence * 100).toFixed(1)}%
          </Text>
        </div>
      )
    }
  ];

  const interactionColumns = [
    {
      title: '交互类型',
      dataIndex: 'interaction_type',
      key: 'interaction_type',
      render: (type: string) => <Tag color="blue">{type}</Tag>
    },
    {
      title: '目标元素',
      dataIndex: 'target_element',
      key: 'target_element'
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '序列',
      dataIndex: 'sequence',
      key: 'sequence',
      render: (seq: number) => <Badge count={seq} style={{ backgroundColor: '#1890ff' }} />
    }
  ];

  /**
   * 根据优先级获取对应的颜色
   * @param priority 优先级字符串
   * @returns 对应的颜色值
   */
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#ff4d4f';
      case 'medium': return '#faad14';
      case 'low': return '#52c41a';
      default: return '#d9d9d9';
    }
  };

  return (
    <ResultsContainer
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <Title level={4} style={{ marginBottom: 24, textAlign: 'center' }}>
          📊 分析结果
        </Title>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={<span><TrophyOutlined />概览</span>} key="overview">
            <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
              <Col span={6}>
                <StatCard>
                  <Statistic
                    title="UI元素"
                    value={result.elements.length}
                    prefix={<BugOutlined />}
                    suffix="个"
                  />
                </StatCard>
              </Col>
              <Col span={6}>
                <StatCard>
                  <Statistic
                    title="交互行为"
                    value={result.interactions.length}
                    prefix={<InteractionOutlined />}
                    suffix="种"
                  />
                </StatCard>
              </Col>
              <Col span={6}>
                <StatCard>
                  <Statistic
                    title="测试用例"
                    value={result.use_cases.length}
                    prefix={<FileTextOutlined />}
                    suffix="个"
                  />
                </StatCard>
              </Col>
              <Col span={6}>
                <StatCard>
                  <Statistic
                    title="处理时间"
                    value={result.processing_time?.toFixed(2) || 0}
                    suffix="秒"
                  />
                </StatCard>
              </Col>
            </Row>

            <Card title="分析总结" style={{ marginBottom: 16 }}>
              <Paragraph>
                {result.analysis_summary}
              </Paragraph>
            </Card>
          </TabPane>

          <TabPane tab={<span><BugOutlined />UI元素</span>} key="elements">
            <Table
              dataSource={result.elements.map((item, index) => ({ ...item, key: index }))}
              columns={elementColumns}
              pagination={{ pageSize: 10 }}
              size="middle"
            />
          </TabPane>

          <TabPane tab={<span><InteractionOutlined />交互分析</span>} key="interactions">
            <Table
              dataSource={result.interactions.map((item, index) => ({ ...item, key: index }))}
              columns={interactionColumns}
              pagination={{ pageSize: 10 }}
              size="middle"
            />
          </TabPane>

          <TabPane tab={<span><FileTextOutlined />测试用例</span>} key="usecases">
            {result.use_cases.map((useCase, index) => (
              <UseCaseCard key={index} style={{ marginBottom: 16 }}>
                <div style={{ marginBottom: 12 }}>
                  <Space>
                    <Title level={5} style={{ margin: 0 }}>{useCase.title}</Title>
                    <Tag color={getPriorityColor(useCase.priority || 'medium')}>
                      {useCase.priority || 'medium'}
                    </Tag>
                  </Space>
                </div>
                
                <Paragraph>{useCase.description}</Paragraph>
                
                <div style={{ marginBottom: 12 }}>
                  <Text strong>测试步骤：</Text>
                  <ol>
                    {useCase.steps.map((step, idx) => (
                      <li key={idx}>{step}</li>
                    ))}
                  </ol>
                </div>
              </UseCaseCard>
            ))}
          </TabPane>
        </Tabs>
      </Card>
    </ResultsContainer>
  );
};

export default AnalysisResults;
