import React, { useState, useRef, useEffect } from 'react';
import { Card, Input, Button, Typography, Space, Avatar, Tag } from 'antd';
import { SendOutlined, RobotOutlined, UserOutlined, BugOutlined, InteractionOutlined, FileTextOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { StreamMessage, MessageType, AgentType } from '../types';

const { TextArea } = Input;
const { Text, Title } = Typography;

const ChatContainer = styled.div`
  height: 600px;
  display: flex;
  flex-direction: column;
`;

const MessagesContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px 8px 0 0;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
`;

const MessageItem = styled(motion.div)<{ isUser: boolean }>`
  display: flex;
  margin-bottom: 16px;
  justify-content: ${props => props.isUser ? 'flex-end' : 'flex-start'};
`;

const MessageContent = styled.div<{ isUser: boolean }>`
  max-width: 70%;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  flex-direction: ${props => props.isUser ? 'row-reverse' : 'row'};
`;

const MessageBubble = styled.div<{ isUser: boolean; messageType: MessageType }>`
  padding: 12px 16px;
  border-radius: 12px;
  background: ${props => {
    if (props.isUser) return '#1890ff';
    switch (props.messageType) {
      case MessageType.AGENT_PROCESSING: return '#f6ffed';
      case MessageType.AGENT_RESULT: return '#fff7e6';
      case MessageType.SYSTEM_INFO: return '#f0f5ff';
      case MessageType.ERROR: return '#fff2f0';
      case MessageType.COMPLETE: return '#f6ffed';
      default: return '#ffffff';
    }
  }};
  color: ${props => props.isUser ? '#ffffff' : '#000000'};
  border: ${props => {
    if (props.isUser) return 'none';
    switch (props.messageType) {
      case MessageType.AGENT_PROCESSING: return '1px solid #b7eb8f';
      case MessageType.AGENT_RESULT: return '1px solid #ffd591';
      case MessageType.SYSTEM_INFO: return '1px solid #91d5ff';
      case MessageType.ERROR: return '1px solid #ffb3b3';
      case MessageType.COMPLETE: return '1px solid #95de64';
      default: return '1px solid #d9d9d9';
    }
  }};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 12px;
    ${props => props.isUser ? 'right: -6px' : 'left: -6px'};
    width: 0;
    height: 0;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    ${props => props.isUser 
      ? 'border-left: 6px solid #1890ff' 
      : `border-right: 6px solid ${
          props.messageType === MessageType.AGENT_PROCESSING ? '#f6ffed' :
          props.messageType === MessageType.AGENT_RESULT ? '#fff7e6' :
          props.messageType === MessageType.SYSTEM_INFO ? '#f0f5ff' :
          props.messageType === MessageType.ERROR ? '#fff2f0' :
          props.messageType === MessageType.COMPLETE ? '#f6ffed' : '#ffffff'
        }`
    };
  }
`;

const InputContainer = styled.div`
  padding: 16px;
  background: white;
  border-radius: 0 0 8px 8px;
  border-top: 1px solid #f0f0f0;
`;

const TypingIndicator = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  margin-bottom: 8px;
`;

const TypingDots = styled.div`
  display: flex;
  gap: 4px;
  
  span {
    width: 6px;
    height: 6px;
    background: #1890ff;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
  }
  
  @keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
  }
`;

/**
 * 分析对话组件的属性接口
 */
interface AnalysisChatProps {
  onSendMessage: (message: string) => void; // 发送消息回调
  messages: StreamMessage[]; // 消息列表
  isAnalyzing: boolean; // 是否正在分析
}

/**
 * 根据智能体类型获取显示信息
 * @param agent 智能体类型
 * @returns 包含图标、名称和颜色的对象
 */
const getAgentInfo = (agent?: AgentType) => {
  switch (agent) {
    case AgentType.ELEMENT_RECOGNITION:
      return { icon: <BugOutlined />, name: '元素识别', color: '#52c41a' };
    case AgentType.INTERACTION_ANALYSIS:
      return { icon: <InteractionOutlined />, name: '交互分析', color: '#1890ff' };
    case AgentType.USECASE_GENERATION:
      return { icon: <FileTextOutlined />, name: '用例生成', color: '#722ed1' };
    default:
      return { icon: <RobotOutlined />, name: '系统', color: '#8c8c8c' };
  }
};

/**
 * 根据消息类型获取对应的标签
 * @param type 消息类型
 * @returns 对应的标签组件
 */
const getMessageTypeTag = (type: MessageType) => {
  switch (type) {
    case MessageType.AGENT_PROCESSING:
      return <Tag color="processing">处理中</Tag>;
    case MessageType.AGENT_RESULT:
      return <Tag color="success">结果</Tag>;
    case MessageType.SYSTEM_INFO:
      return <Tag color="blue">系统</Tag>;
    case MessageType.ERROR:
      return <Tag color="error">错误</Tag>;
    case MessageType.COMPLETE:
      return <Tag color="success">完成</Tag>;
    default:
      return null;
  }
};

/**
 * 分析对话组件
 * 显示智能体分析过程中的实时对话
 */
const AnalysisChat: React.FC<AnalysisChatProps> = ({ onSendMessage, messages, isAnalyzing }) => {
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSend = () => {
    if (inputValue.trim() && !isAnalyzing) {
      onSendMessage(inputValue.trim());
      setInputValue('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <Card>
      <Title level={4} style={{ marginBottom: 16, textAlign: 'center' }}>
        🤖 智能体对话
      </Title>
      
      <ChatContainer>
        <MessagesContainer>
          <AnimatePresence>
            {messages.map((message, index) => {
              const isUser = message.type === MessageType.USER_INPUT;
              const agentInfo = getAgentInfo(message.agent);
              
              return (
                <MessageItem
                  key={message.id}
                  isUser={isUser}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <MessageContent isUser={isUser}>
                    <Avatar
                      icon={isUser ? <UserOutlined /> : agentInfo.icon}
                      style={{ 
                        backgroundColor: isUser ? '#1890ff' : agentInfo.color,
                        flexShrink: 0
                      }}
                    />
                    <div>
                      <MessageBubble isUser={isUser} messageType={message.type}>
                        {!isUser && (
                          <div style={{ marginBottom: 4 }}>
                            <Space>
                              <Text strong style={{ fontSize: '12px' }}>
                                {agentInfo.name}
                              </Text>
                              {getMessageTypeTag(message.type)}
                            </Space>
                          </div>
                        )}
                        <div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                          {message.content}
                        </div>
                        <div style={{ 
                          fontSize: '11px', 
                          opacity: 0.7, 
                          marginTop: 4,
                          textAlign: isUser ? 'right' : 'left'
                        }}>
                          {new Date(message.timestamp).toLocaleTimeString()}
                        </div>
                      </MessageBubble>
                    </div>
                  </MessageContent>
                </MessageItem>
              );
            })}
          </AnimatePresence>
          
          {isAnalyzing && (
            <TypingIndicator
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <Avatar icon={<RobotOutlined />} size="small" />
              <Text type="secondary">智能体正在分析中</Text>
              <TypingDots>
                <span></span>
                <span></span>
                <span></span>
              </TypingDots>
            </TypingIndicator>
          )}
          
          <div ref={messagesEndRef} />
        </MessagesContainer>
        
        <InputContainer>
          <Space.Compact style={{ width: '100%' }}>
            <TextArea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="输入您的问题或对图片的描述..."
              autoSize={{ minRows: 1, maxRows: 3 }}
              disabled={isAnalyzing}
            />
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleSend}
              disabled={!inputValue.trim() || isAnalyzing}
              loading={isAnalyzing}
            >
              发送
            </Button>
          </Space.Compact>
        </InputContainer>
      </ChatContainer>
    </Card>
  );
};

export default AnalysisChat;
