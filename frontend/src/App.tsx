import React, { useState, useEffect } from 'react';
import { Layout, Row, Col, Typography, message, Button, Space } from 'antd';
import { RobotOutlined, GithubOutlined, HeartOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { motion } from 'framer-motion';

import ImageUpload from './components/ImageUpload';
import AnalysisChat from './components/AnalysisChat';
import AnalysisResults from './components/AnalysisResults';
import { ApiService } from './services/api';
import { 
  UploadResponse, 
  StreamMessage, 
  MessageType, 
  AnalysisResult 
} from './types';

const { Header, Content, Footer } = Layout;
const { Title, Text } = Typography;

const StyledLayout = styled(Layout)`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
`;

const StyledHeader = styled(Header)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const StyledContent = styled(Content)`
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
`;

const StyledFooter = styled(Footer)`
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
`;

const LogoContainer = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const HeaderActions = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

/**
 * 主应用组件
 * 图片分析智能体系统的主界面
 */
const App: React.FC = () => {
  const [uploadedFile, setUploadedFile] = useState<UploadResponse | null>(null); // 上传的文件信息
  const [messages, setMessages] = useState<StreamMessage[]>([]); // 消息列表
  const [isAnalyzing, setIsAnalyzing] = useState(false); // 是否正在分析
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null); // 分析结果
  const [eventSource, setEventSource] = useState<EventSource | null>(null); // SSE连接

  // 实时收集的分析数据
  const [realtimeAnalysis, setRealtimeAnalysis] = useState<{
    elements: any[];
    interactions: any[];
    use_cases: any[];
    elementsCount: number;
    interactionsCount: number;
    useCasesCount: number;
  }>({
    elements: [],
    interactions: [],
    use_cases: [],
    elementsCount: 0,
    interactionsCount: 0,
    useCasesCount: 0
  });

  // 组件卸载时清理EventSource连接
  useEffect(() => {
    return () => {
      if (eventSource) {
        eventSource.close();
      }
    };
  }, [eventSource]);

  /**
   * 处理文件上传成功
   * @param response 上传响应数据
   */
  const handleUploadSuccess = (response: UploadResponse) => {
    setUploadedFile(response);
    setMessages([]);
    setAnalysisResult(null);
    message.success('图片上传成功！现在可以开始分析了。');
  };

  /**
   * 处理文件上传失败
   * @param error 错误信息
   */
  const handleUploadError = (error: string) => {
    message.error(`上传失败：${error}`);
  };

  /**
   * 发送消息并开始分析
   * @param userMessage 用户输入的消息
   */
  const handleSendMessage = async (userMessage: string) => {
    if (!uploadedFile?.file_path) {
      message.warning('请先上传图片！');
      return;
    }

    // 添加用户消息
    const userMsg: StreamMessage = {
      id: Date.now().toString(),
      type: MessageType.USER_INPUT,
      content: userMessage,
      timestamp: new Date().toISOString()
    };
    setMessages(prev => [...prev, userMsg]);

    setIsAnalyzing(true);
    setAnalysisResult(null);

    try {
      // 关闭之前的连接
      if (eventSource) {
        eventSource.close();
      }

      // 创建新的SSE连接
      const newEventSource = ApiService.createAnalysisStream(
        uploadedFile.file_path,
        userMessage
      );
      setEventSource(newEventSource);

      newEventSource.onmessage = (event) => {
        const data = event.data;
        
        if (data === '[DONE]') {
          newEventSource.close();
          setEventSource(null);
          setIsAnalyzing(false);
          return;
        }

        try {
          const message: StreamMessage = JSON.parse(data);
          setMessages(prev => [...prev, message]);

          // 实时更新分析结果计数
          if (message.type === MessageType.AGENT_RESULT && message.metadata) {
            const metadata = message.metadata;

            if (metadata.elements_count !== undefined) {
              setRealtimeAnalysis(prev => ({
                ...prev,
                elementsCount: metadata.elements_count
              }));
            }

            if (metadata.interactions_count !== undefined) {
              setRealtimeAnalysis(prev => ({
                ...prev,
                interactionsCount: metadata.interactions_count
              }));
            }

            if (metadata.use_cases_count !== undefined) {
              setRealtimeAnalysis(prev => ({
                ...prev,
                useCasesCount: metadata.use_cases_count
              }));
            }
          }

          // 如果是完成消息，构建最终结果
          if (message.type === MessageType.COMPLETE) {
            console.log('收到完成消息，构建最终结果');

            // 使用实时收集的数据构建结果
            const finalResult: AnalysisResult = {
              request_id: message.metadata?.request_id || 'unknown',
              image_path: uploadedFile?.file_path || '',
              elements: [], // 这里可以根据需要填充实际数据
              interactions: [],
              use_cases: [],
              analysis_summary: `分析完成！识别了 ${realtimeAnalysis.elementsCount} 个UI元素，${realtimeAnalysis.interactionsCount} 种交互行为，生成了 ${realtimeAnalysis.useCasesCount} 个测试用例。`,
              processing_time: message.metadata?.processing_time || 0,
              created_at: new Date().toISOString()
            };

            setAnalysisResult(finalResult);
            console.log('最终结果已设置:', finalResult);
          }
        } catch (error) {
          console.error('解析SSE消息失败:', error);
        }
      };

      newEventSource.onerror = (error) => {
        console.error('SSE连接错误:', error);
        newEventSource.close();
        setEventSource(null);
        setIsAnalyzing(false);
        
        const errorMsg: StreamMessage = {
          id: Date.now().toString(),
          type: MessageType.ERROR,
          content: '连接中断，分析失败',
          timestamp: new Date().toISOString()
        };
        setMessages(prev => [...prev, errorMsg]);
        message.error('分析过程中发生错误，请重试');
      };

    } catch (error: any) {
      console.error('启动分析失败:', error);
      setIsAnalyzing(false);
      message.error(`启动分析失败：${error.message}`);
    }
  };

  // 注意：fetchCompleteResult 函数已被移除，现在使用实时结果更新机制

  /**
   * 重新开始分析
   * 清空所有状态并重置界面
   */
  const handleRestart = () => {
    setUploadedFile(null);
    setMessages([]);
    setAnalysisResult(null);
    setIsAnalyzing(false);

    // 重置实时分析数据
    setRealtimeAnalysis({
      elements: [],
      interactions: [],
      use_cases: [],
      elementsCount: 0,
      interactionsCount: 0,
      useCasesCount: 0
    });

    if (eventSource) {
      eventSource.close();
      setEventSource(null);
    }
  };

  return (
    <StyledLayout>
      <StyledHeader>
        <LogoContainer
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <RobotOutlined style={{ fontSize: 32, color: '#1890ff' }} />
          <div>
            <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
              图片分析智能体
            </Title>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              基于 AutoGen 0.6.2 的多智能体系统
            </Text>
          </div>
        </LogoContainer>

        <HeaderActions>
          <Button 
            type="primary" 
            ghost 
            icon={<GithubOutlined />}
            href="https://github.com/microsoft/autogen"
            target="_blank"
          >
            AutoGen
          </Button>
          {uploadedFile && (
            <Button onClick={handleRestart}>
              重新开始
            </Button>
          )}
        </HeaderActions>
      </StyledHeader>

      <StyledContent>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Row gutter={[24, 24]}>
            {/* 左侧：上传和对话 */}
            <Col xs={24} lg={12}>
              <Space direction="vertical" style={{ width: '100%' }} size="large">
                <ImageUpload
                  onUploadSuccess={handleUploadSuccess}
                  onUploadError={handleUploadError}
                />
                
                {uploadedFile && (
                  <AnalysisChat
                    onSendMessage={handleSendMessage}
                    messages={messages}
                    isAnalyzing={isAnalyzing}
                  />
                )}
              </Space>
            </Col>

            {/* 右侧：分析结果 */}
            <Col xs={24} lg={12}>
              <AnalysisResults
                result={analysisResult}
                loading={isAnalyzing}
              />
            </Col>
          </Row>
        </motion.div>
      </StyledContent>

      <StyledFooter>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1, duration: 0.5 }}
        >
          <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
            图片分析智能体系统 ©2025 Created with <HeartOutlined style={{ color: '#ff4d4f' }} /> 
            using AutoGen 0.6.2 & FastAPI
          </Text>
        </motion.div>
      </StyledFooter>
    </StyledLayout>
  );
};

export default App;
