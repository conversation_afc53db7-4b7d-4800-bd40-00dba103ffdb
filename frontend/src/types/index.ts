// 智能体类型枚举
export enum AgentType {
  ELEMENT_RECOGNITION = "element_recognition", // 元素识别智能体
  INTERACTION_ANALYSIS = "interaction_analysis", // 交互分析智能体
  USECASE_GENERATION = "usecase_generation" // 用例生成智能体
}

// 消息类型枚举
export enum MessageType {
  USER_INPUT = "user_input", // 用户输入
  AGENT_PROCESSING = "agent_processing", // 智能体处理中
  AGENT_RESULT = "agent_result", // 智能体结果
  SYSTEM_INFO = "system_info", // 系统信息
  ERROR = "error", // 错误信息
  COMPLETE = "complete" // 完成
}

// 流式消息接口
export interface StreamMessage {
  id: string; // 消息ID
  type: MessageType; // 消息类型
  agent?: AgentType; // 发送消息的智能体
  content: string; // 消息内容
  timestamp: string; // 时间戳
  metadata?: Record<string, any>; // 元数据
}

// UI元素信息接口
export interface ElementInfo {
  element_type: string; // 元素类型
  position: Record<string, number>; // 元素位置坐标
  size: Record<string, number>; // 元素大小
  text?: string; // 元素文本内容
  attributes?: Record<string, any>; // 元素属性
  confidence: number; // 识别置信度
}

// 交互信息接口
export interface InteractionInfo {
  interaction_type: string; // 交互类型
  target_element: string; // 目标元素
  description: string; // 交互描述
  sequence: number; // 交互序列
  conditions?: string[]; // 交互条件
}

// 用例信息接口
export interface UseCaseInfo {
  title: string; // 用例标题
  description: string; // 用例描述
  steps: string[]; // 用例步骤
  preconditions?: string[]; // 前置条件
  expected_results?: string[]; // 预期结果
  priority?: string; // 优先级
}

// 分析结果接口
export interface AnalysisResult {
  request_id: string; // 请求ID
  image_path: string; // 图片路径
  elements: ElementInfo[]; // 识别的UI元素
  interactions: InteractionInfo[]; // 交互分析结果
  use_cases: UseCaseInfo[]; // 生成的用例
  analysis_summary: string; // 分析总结
  created_at: string; // 创建时间
  processing_time?: number; // 处理时间(秒)
}

// 上传响应接口
export interface UploadResponse {
  success: boolean; // 上传是否成功
  file_path?: string; // 文件路径
  file_size?: number; // 文件大小
  message: string; // 响应消息
}

// 文件信息接口
export interface FileInfo {
  file_path: string; // 文件路径
  file_size: number; // 文件大小
  width: number; // 图片宽度
  height: number; // 图片高度
  format: string; // 图片格式
}

// 分析请求接口
export interface AnalysisRequest {
  image_path: string; // 图片路径
  user_message?: string; // 用户附加描述
  analysis_options?: Record<string, any>; // 分析选项
}

// 错误响应接口
export interface ErrorResponse {
  error: boolean; // 是否为错误
  message: string; // 错误消息
  code?: string; // 错误代码
  details?: Record<string, any>; // 错误详情
}
