#!/usr/bin/env python3
"""
快速启动脚本 - 解决依赖安装问题
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, cwd=None, check=True):
    """运行命令"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            text=True,
            check=check
        )
        return True
    except subprocess.CalledProcessError:
        return False

def install_basic_deps():
    """安装基础依赖"""
    print("📦 安装基础依赖...")
    
    basic_packages = [
        "fastapi==0.104.1",
        "uvicorn[standard]==0.24.0", 
        "python-multipart==0.0.6",
        "python-dotenv==1.0.0",
        "pillow==10.1.0",
        "aiofiles==23.2.1",
        "pydantic==2.5.0",
        "httpx==0.25.2",
        "openai==1.3.0",
        "requests==2.31.0"
    ]
    
    for package in basic_packages:
        print(f"  安装 {package}...")
        if run_command(f"{sys.executable} -m pip install {package}", check=False):
            print(f"  ✅ {package}")
        else:
            print(f"  ❌ {package} 失败")

def install_autogen():
    """尝试安装 AutoGen"""
    print("\n🤖 安装 AutoGen...")
    
    # 尝试不同的安装方式
    install_methods = [
        # 方法1：指定版本
        ["autogen-agentchat==0.6.1", "autogen-ext[openai]==0.6.1"],
        # 方法2：最新版本
        ["autogen-agentchat", "autogen-ext[openai]"],
        # 方法3：从 GitHub
        ["git+https://github.com/microsoft/autogen.git@0.6.1#subdirectory=python/packages/autogen-agentchat"],
        # 方法4：预发布版本
        ["--pre", "autogen-agentchat", "autogen-ext[openai]"]
    ]
    
    for i, method in enumerate(install_methods, 1):
        print(f"  尝试方法 {i}: {' '.join(method)}")
        
        if isinstance(method, list) and len(method) > 1 and not method[0].startswith("--"):
            # 多个包
            success = True
            for package in method:
                if not run_command(f"{sys.executable} -m pip install {package}", check=False):
                    success = False
                    break
        else:
            # 单个命令
            cmd = f"{sys.executable} -m pip install {' '.join(method)}"
            success = run_command(cmd, check=False)
        
        if success:
            print(f"  ✅ 方法 {i} 成功")
            return True
        else:
            print(f"  ❌ 方法 {i} 失败")
    
    print("  ⚠️  AutoGen 安装失败，将使用简化版本")
    return False

def create_simple_env():
    """创建简单的环境配置"""
    env_file = Path("backend/.env")
    if env_file.exists():
        print("✅ .env 文件已存在")
        return
    
    print("📝 创建 .env 文件...")
    
    env_content = """# OpenAI 配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# DeepSeek 配置 (用于用例生成智能体)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1

# UI-TARS 配置 (用于元素识别和交互分析智能体)
UITARS_API_KEY=your_uitars_api_key_here
UITARS_BASE_URL=https://api.ui-tars.com/v1

# 应用程序配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,bmp,webp

# 服务器配置
HOST=0.0.0.0
PORT=8000
DEBUG=True
"""
    
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ .env 文件已创建")
    print("📝 请编辑 backend/.env 文件，填入你的 API Keys")

def create_simple_backend():
    """创建简化的后端版本（如果 AutoGen 安装失败）"""
    print("🔧 创建简化后端...")
    
    simple_main = """#!/usr/bin/env python3
'''
简化版后端 - 不依赖 AutoGen
'''

from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import uvicorn
import os
from pathlib import Path

app = FastAPI(title="图片分析系统 (简化版)")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建上传目录
upload_dir = Path("uploads")
upload_dir.mkdir(exist_ok=True)
app.mount("/uploads", StaticFiles(directory=upload_dir), name="uploads")

@app.get("/")
async def root():
    return {"message": "图片分析系统 (简化版)", "status": "running"}

@app.get("/health")
async def health():
    return {"status": "healthy", "version": "simple"}

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    try:
        file_path = upload_dir / file.filename
        with open(file_path, "wb") as f:
            content = await file.read()
            f.write(content)
        
        return {
            "success": True,
            "file_path": str(file_path),
            "file_size": len(content),
            "message": "文件上传成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/analyze")
async def analyze_image(
    image_path: str = Form(...),
    user_message: str = Form(None)
):
    return {
        "request_id": "demo",
        "image_path": image_path,
        "elements": [
            {"element_type": "button", "text": "示例按钮", "confidence": 0.9},
            {"element_type": "input", "text": "", "confidence": 0.8}
        ],
        "interactions": [
            {"interaction_type": "click", "target_element": "button", "description": "点击按钮"}
        ],
        "use_cases": [
            {"title": "示例测试用例", "description": "这是一个示例", "steps": ["步骤1", "步骤2"]}
        ],
        "analysis_summary": "这是简化版演示，请安装 AutoGen 获得完整功能",
        "created_at": "2025-01-09T00:00:00Z"
    }

if __name__ == "__main__":
    print("🚀 启动简化版后端服务...")
    print("⚠️  这是简化版本，请安装 AutoGen 获得完整功能")
    uvicorn.run(app, host="0.0.0.0", port=8000)
"""
    
    simple_main_file = Path("backend/simple_main.py")
    with open(simple_main_file, 'w', encoding='utf-8') as f:
        f.write(simple_main)
    
    print("✅ 简化后端已创建: backend/simple_main.py")

def main():
    """主函数"""
    print("🚀 图片分析智能体系统 - 快速启动")
    print("="*50)
    
    # 1. 安装基础依赖
    install_basic_deps()
    
    # 2. 尝试安装 AutoGen
    autogen_success = install_autogen()
    
    # 3. 创建环境配置
    create_simple_env()
    
    # 4. 如果 AutoGen 安装失败，创建简化版本
    if not autogen_success:
        create_simple_backend()
    
    print("\n" + "="*50)
    print("🎉 快速启动完成！")
    print("="*50)
    
    if autogen_success:
        print("✅ AutoGen 安装成功，可以使用完整功能")
        print("\n🚀 启动命令:")
        print("  cd backend && python main.py")
    else:
        print("⚠️  AutoGen 安装失败，使用简化版本")
        print("\n🚀 启动命令:")
        print("  cd backend && python simple_main.py")
    
    print("\n📝 下一步:")
    print("1. 编辑 backend/.env 文件，填入 API Keys")
    print("2. 启动后端服务")
    print("3. 安装前端依赖: cd frontend && npm install")
    print("4. 启动前端服务: cd frontend && npm start")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 启动被用户中断")
    except Exception as e:
        print(f"\n❌ 启动过程中发生错误: {e}")
