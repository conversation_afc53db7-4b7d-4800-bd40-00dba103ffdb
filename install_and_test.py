#!/usr/bin/env python3
"""
安装依赖并测试系统
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True, 
            check=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def install_backend_deps():
    """安装后端依赖"""
    print("📦 安装后端依赖...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ backend 目录不存在")
        return False
    
    # 升级 pip
    print("  升级 pip...")
    success, output = run_command(f"{sys.executable} -m pip install --upgrade pip")
    if not success:
        print(f"❌ pip 升级失败: {output}")
        return False
    
    # 安装依赖
    print("  安装 Python 包...")
    success, output = run_command(
        f"{sys.executable} -m pip install -r requirements.txt", 
        cwd=backend_dir
    )
    if not success:
        print(f"❌ 依赖安装失败: {output}")
        print("\n🔧 尝试单独安装关键包...")
        
        # 尝试单独安装关键包
        key_packages = [
            "fastapi==0.104.1",
            "uvicorn[standard]==0.24.0", 
            "python-multipart==0.0.6",
            "python-dotenv==1.0.0",
            "pillow==10.1.0",
            "aiofiles==23.2.1",
            "pydantic==2.5.0",
            "httpx==0.25.2",
            "openai==1.3.0",
            "requests==2.31.0"
        ]
        
        for package in key_packages:
            print(f"    安装 {package}...")
            success, output = run_command(f"{sys.executable} -m pip install {package}")
            if not success:
                print(f"    ❌ {package} 安装失败")
            else:
                print(f"    ✅ {package} 安装成功")
        
        # 尝试安装 AutoGen
        print("  尝试安装 AutoGen...")
        autogen_packages = [
            "autogen-agentchat==0.6.1",
            "autogen-ext[openai]==0.6.1"
        ]
        
        for package in autogen_packages:
            print(f"    安装 {package}...")
            success, output = run_command(f"{sys.executable} -m pip install {package}")
            if not success:
                print(f"    ❌ {package} 安装失败: {output}")
                print(f"    尝试不指定版本...")
                base_package = package.split("==")[0]
                success, output = run_command(f"{sys.executable} -m pip install {base_package}")
                if success:
                    print(f"    ✅ {base_package} 安装成功")
                else:
                    print(f"    ❌ {base_package} 也安装失败")
            else:
                print(f"    ✅ {package} 安装成功")
    
    print("✅ 后端依赖安装完成")
    return True

def test_autogen():
    """测试 AutoGen"""
    print("\n🧪 测试 AutoGen...")
    
    success, output = run_command(
        f"{sys.executable} test_autogen.py", 
        cwd=Path("backend")
    )
    
    if success:
        print("✅ AutoGen 测试通过")
        print(output)
        return True
    else:
        print("❌ AutoGen 测试失败")
        print(output)
        return False

def check_env_file():
    """检查环境变量文件"""
    print("\n🔧 检查环境配置...")
    
    env_file = Path("backend/.env")
    if not env_file.exists():
        print("⚠️  .env 文件不存在，创建示例文件...")
        example_file = Path("backend/.env.example")
        if example_file.exists():
            import shutil
            shutil.copy(example_file, env_file)
            print("✅ 已创建 .env 文件")
            print("📝 请编辑 backend/.env 文件，填入你的 API Keys:")
            print("   - OPENAI_API_KEY")
            print("   - DEEPSEEK_API_KEY") 
            print("   - UITARS_API_KEY (可选)")
            return False
        else:
            print("❌ .env.example 文件也不存在")
            return False
    else:
        print("✅ .env 文件存在")
        
        # 检查关键配置
        with open(env_file, 'r') as f:
            content = f.read()
            
        missing_keys = []
        required_keys = ["OPENAI_API_KEY", "DEEPSEEK_API_KEY"]
        
        for key in required_keys:
            if f"{key}=your_" in content or f"{key}=" not in content:
                missing_keys.append(key)
        
        if missing_keys:
            print(f"⚠️  请在 .env 文件中配置以下 API Keys: {', '.join(missing_keys)}")
            return False
        else:
            print("✅ API Keys 已配置")
            return True

def install_frontend_deps():
    """安装前端依赖"""
    print("\n📦 安装前端依赖...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ frontend 目录不存在")
        return False
    
    # 检查 Node.js
    success, output = run_command("node --version")
    if not success:
        print("❌ Node.js 未安装，请先安装 Node.js 16+")
        return False
    
    print(f"✅ Node.js 版本: {output.strip()}")
    
    # 安装依赖
    print("  安装 npm 包...")
    success, output = run_command("npm install", cwd=frontend_dir)
    if not success:
        print(f"❌ 前端依赖安装失败: {output}")
        return False
    
    print("✅ 前端依赖安装完成")
    return True

def main():
    """主函数"""
    print("🚀 图片分析智能体系统 - 安装和测试脚本")
    print("="*60)
    
    # 1. 安装后端依赖
    if not install_backend_deps():
        print("❌ 后端依赖安装失败")
        return False
    
    # 2. 检查环境配置
    env_ok = check_env_file()
    
    # 3. 测试 AutoGen (如果环境配置正确)
    if env_ok:
        if not test_autogen():
            print("⚠️  AutoGen 测试失败，但可以继续")
    else:
        print("⚠️  跳过 AutoGen 测试，请先配置 API Keys")
    
    # 4. 安装前端依赖
    if not install_frontend_deps():
        print("❌ 前端依赖安装失败")
        return False
    
    print("\n" + "="*60)
    print("🎉 安装完成！")
    print("="*60)
    
    if env_ok:
        print("✅ 系统已准备就绪，可以启动服务")
        print("\n🚀 启动命令:")
        print("  后端: cd backend && python main.py")
        print("  前端: cd frontend && npm start")
        print("  或者: python start.py (一键启动)")
    else:
        print("⚠️  请先配置 API Keys，然后重新运行测试")
        print("\n📝 配置步骤:")
        print("1. 编辑 backend/.env 文件")
        print("2. 填入你的 API Keys")
        print("3. 运行: python backend/test_autogen.py")
        print("4. 启动系统: python start.py")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程中发生错误: {e}")
        sys.exit(1)
