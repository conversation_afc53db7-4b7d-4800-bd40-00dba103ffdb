#!/usr/bin/env python3
"""
系统测试脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加backend路径到sys.path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

async def test_config():
    """测试配置"""
    print("🔧 测试配置...")
    try:
        from utils.config import Config
        
        # 检查必要的配置
        required_keys = ['OPENAI_API_KEY', 'DEEPSEEK_API_KEY']
        missing_keys = []
        
        for key in required_keys:
            value = getattr(Config, key, '')
            if not value:
                missing_keys.append(key)
        
        if missing_keys:
            print(f"❌ 缺少配置: {', '.join(missing_keys)}")
            print("请在 backend/.env 文件中配置这些API Keys")
            return False
        else:
            print("✅ 配置检查通过")
            return True
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

async def test_agents():
    """测试智能体"""
    print("\n🤖 测试智能体...")
    try:
        from agents.element_recognition import ElementRecognitionAgent
        from agents.interaction_analysis import InteractionAnalysisAgent
        from agents.usecase_generation import UseCaseGenerationAgent
        from utils.config import Config
        
        # 测试元素识别智能体
        print("  测试元素识别智能体...")
        element_agent = ElementRecognitionAgent(
            api_key=Config.OPENAI_API_KEY,
            base_url=Config.OPENAI_BASE_URL
        )
        print("  ✅ 元素识别智能体初始化成功")
        
        # 测试交互分析智能体
        print("  测试交互分析智能体...")
        interaction_agent = InteractionAnalysisAgent(
            api_key=Config.OPENAI_API_KEY,
            base_url=Config.OPENAI_BASE_URL
        )
        print("  ✅ 交互分析智能体初始化成功")
        
        # 测试用例生成智能体
        print("  测试用例生成智能体...")
        usecase_agent = UseCaseGenerationAgent(
            api_key=Config.DEEPSEEK_API_KEY,
            base_url=Config.DEEPSEEK_BASE_URL
        )
        print("  ✅ 用例生成智能体初始化成功")
        
        # 清理
        await element_agent.close()
        await interaction_agent.close()
        await usecase_agent.close()
        
        print("✅ 智能体测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 智能体测试失败: {e}")
        return False

async def test_services():
    """测试服务"""
    print("\n🔧 测试服务...")
    try:
        from services.image_service import ImageService
        from services.agent_service import AgentService
        
        # 测试图片服务
        print("  测试图片服务...")
        image_service = ImageService()
        print("  ✅ 图片服务初始化成功")
        
        # 测试智能体服务
        print("  测试智能体服务...")
        agent_service = AgentService()
        print("  ✅ 智能体服务初始化成功")
        
        # 清理
        await agent_service.close()
        
        print("✅ 服务测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 服务测试失败: {e}")
        return False

async def test_api():
    """测试API"""
    print("\n🌐 测试API...")
    try:
        import httpx
        
        # 启动FastAPI应用进行测试
        print("  测试健康检查接口...")
        
        # 这里可以添加更多的API测试
        print("  ✅ API基础结构正常")
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_frontend():
    """测试前端"""
    print("\n🎨 测试前端...")
    try:
        frontend_path = Path("frontend")
        
        # 检查package.json
        package_json = frontend_path / "package.json"
        if not package_json.exists():
            print("❌ 未找到 frontend/package.json")
            return False
        
        # 检查主要文件
        main_files = [
            "src/App.tsx",
            "src/index.tsx",
            "src/types/index.ts",
            "src/services/api.ts"
        ]
        
        for file_path in main_files:
            if not (frontend_path / file_path).exists():
                print(f"❌ 未找到 {file_path}")
                return False
        
        print("✅ 前端文件结构正常")
        return True
        
    except Exception as e:
        print(f"❌ 前端测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 开始系统测试...\n")
    
    tests = [
        ("配置测试", test_config()),
        ("智能体测试", test_agents()),
        ("服务测试", test_services()),
        ("API测试", test_api()),
        ("前端测试", test_frontend())
    ]
    
    results = []
    for test_name, test_coro in tests:
        if asyncio.iscoroutine(test_coro):
            result = await test_coro
        else:
            result = test_coro
        results.append((test_name, result))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print("="*50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常运行。")
        print("\n💡 下一步:")
        print("1. 运行 'python start.py' 启动系统")
        print("2. 或者分别启动后端和前端:")
        print("   - 后端: cd backend && python main.py")
        print("   - 前端: cd frontend && npm start")
    else:
        print("⚠️  部分测试失败，请检查配置和依赖。")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)
