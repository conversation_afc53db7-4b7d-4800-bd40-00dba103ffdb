# 使用指南

本指南将帮助您快速上手图片分析智能体系统。

## 🚀 快速开始

### 1. 系统启动

```bash
# 方法一：使用启动脚本（推荐）
python start.py

# 方法二：手动启动
# 启动后端
cd backend
python main.py

# 启动前端（新终端）
cd frontend
npm start
```

### 2. 访问系统

打开浏览器访问：`http://localhost:3000`

## 📸 使用流程

### 步骤1：上传图片

1. **拖拽上传**：将图片文件拖拽到上传区域
2. **点击上传**：点击上传区域选择文件
3. **支持格式**：JPG、PNG、GIF、BMP、WebP
4. **文件大小**：最大 10MB

![上传界面示例](docs/images/upload-demo.png)

### 步骤2：开始分析

1. 在对话框中输入对图片的描述或问题
2. 点击"发送"按钮开始分析
3. 系统将自动启动多智能体分析流程

**示例输入**：
- "这是一个登录页面，请分析其中的UI元素"
- "分析这个移动应用界面的交互流程"
- "为这个网页生成测试用例"

### 步骤3：查看结果

分析完成后，您可以在右侧查看详细结果：

#### 📊 概览标签
- 统计信息：元素数量、交互类型、用例数量
- 处理时间：分析耗时
- 分析总结：整体分析结果

#### 🔍 UI元素标签
- 元素列表：识别的所有UI元素
- 元素详情：类型、位置、属性、置信度
- 可视化展示：置信度条形图

#### 🎯 交互分析标签
- 交互类型：点击、输入、滑动等
- 操作流程：用户可能的操作路径
- 交互条件：触发条件和依赖关系

#### 📝 测试用例标签
- 功能测试：正常流程测试
- 异常测试：边界值和错误处理
- 用户体验：易用性和可访问性
- 兼容性：跨平台和设备测试

## 🤖 智能体介绍

### 元素识别智能体
- **模型**：GPT-4o（支持视觉）
- **功能**：识别图片中的UI元素
- **输出**：
  - 元素类型（按钮、输入框、标签等）
  - 位置坐标和大小
  - 文本内容
  - 属性信息
  - 识别置信度

### 交互分析智能体
- **模型**：GPT-4o
- **功能**：分析用户交互行为
- **输出**：
  - 交互类型（点击、拖拽、输入等）
  - 操作序列和流程
  - 交互条件和依赖
  - 用户体验评估

### 用例生成智能体
- **模型**：DeepSeek Chat
- **功能**：生成测试用例
- **输出**：
  - 功能测试用例
  - UI测试用例
  - 兼容性测试用例
  - 安全性测试用例

## 💡 使用技巧

### 1. 图片质量建议

- **分辨率**：建议 1920x1080 或更高
- **清晰度**：确保界面元素清晰可见
- **完整性**：包含完整的界面内容
- **格式**：PNG格式保真度最高

### 2. 描述输入建议

**好的描述示例**：
```
这是一个电商网站的商品详情页面，包含商品图片、价格、购买按钮等元素。
请重点分析购买流程的交互设计。
```

**避免的描述**：
```
分析这个图片
```

### 3. 结果解读

#### 置信度说明
- **绿色（80%+）**：识别准确度很高
- **黄色（60-80%）**：识别准确度中等
- **红色（<60%）**：识别准确度较低，需要人工确认

#### 优先级说明
- **High**：核心功能，必须测试
- **Medium**：重要功能，建议测试
- **Low**：辅助功能，可选测试

## 🔧 高级功能

### 1. 批量分析

虽然当前版本不支持批量上传，但您可以：
1. 逐个上传相关界面截图
2. 在描述中说明界面间的关系
3. 系统会考虑上下文进行分析

### 2. 自定义分析重点

在描述中指定分析重点：
```
重点分析表单验证逻辑
关注移动端适配情况
专注于无障碍访问设计
```

### 3. 结果导出

目前支持：
- 复制分析结果文本
- 截图保存结果页面
- 未来版本将支持PDF/Excel导出

## 🐛 常见问题

### Q1: 上传失败怎么办？
**A**: 检查以下几点：
- 文件格式是否支持
- 文件大小是否超过10MB
- 网络连接是否正常
- 浏览器是否支持（建议Chrome/Firefox）

### Q2: 分析时间很长怎么办？
**A**: 分析时间取决于：
- 图片复杂度
- 网络延迟
- API响应速度
- 一般需要30秒-2分钟

### Q3: 识别结果不准确怎么办？
**A**: 可以尝试：
- 提供更清晰的图片
- 在描述中补充上下文信息
- 重新分析或调整描述

### Q4: 系统卡住不响应怎么办？
**A**: 解决方法：
- 刷新页面重试
- 检查浏览器控制台错误
- 重启后端服务
- 检查API配置

## 📞 技术支持

### 日志查看

**后端日志**：
```bash
# 查看实时日志
tail -f backend/logs/app.log

# 查看错误日志
grep ERROR backend/logs/app.log
```

**前端日志**：
- 打开浏览器开发者工具
- 查看Console标签页
- 查看Network标签页的请求状态

### 配置检查

```bash
# 运行系统测试
python test_system.py

# 检查API连接
curl http://localhost:8000/health
```

### 性能监控

- **内存使用**：建议不超过4GB
- **CPU使用**：分析时可能达到80%+
- **网络带宽**：上传和API调用需要稳定网络

## 🔄 版本更新

### 检查更新
```bash
git pull origin main
```

### 更新依赖
```bash
# 后端
cd backend
pip install -r requirements.txt

# 前端
cd frontend
npm install
```

### 重启服务
```bash
# 重启后端
pm2 restart ui-automation-backend

# 重新构建前端
npm run build
```

## 📚 更多资源

- [AutoGen 官方文档](https://microsoft.github.io/autogen/)
- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [React 文档](https://reactjs.org/)
- [项目GitHub仓库](https://github.com/your-repo)

如有其他问题，请查看项目文档或提交Issue。
