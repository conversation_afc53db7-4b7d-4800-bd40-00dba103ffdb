# 部署指南

本文档介绍如何部署图片分析智能体系统到生产环境。

## 🏗️ 部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Nginx       │────│   FastAPI       │────│   AutoGen       │
│   (反向代理)     │    │   (后端API)      │    │   (智能体)       │
│   静态文件服务   │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🐳 Docker 部署

### 1. 创建 Dockerfile (后端)

```dockerfile
# backend/Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建上传目录
RUN mkdir -p uploads

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. 创建 Dockerfile (前端)

```dockerfile
# frontend/Dockerfile
FROM node:18-alpine as builder

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产环境
FROM nginx:alpine

# 复制构建结果
COPY --from=builder /app/build /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### 3. 创建 docker-compose.yml

```yaml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - UITARS_API_KEY=${UITARS_API_KEY}
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
```

## 🌐 传统部署

### 1. 服务器要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+
- **内存**: 最少 4GB，推荐 8GB+
- **CPU**: 最少 2核，推荐 4核+
- **存储**: 最少 20GB 可用空间
- **网络**: 稳定的互联网连接

### 2. 安装依赖

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Python 3.11
sudo apt install python3.11 python3.11-venv python3.11-dev -y

# 安装Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装Nginx
sudo apt install nginx -y

# 安装PM2 (进程管理器)
sudo npm install -g pm2
```

### 3. 部署后端

```bash
# 创建应用目录
sudo mkdir -p /opt/ui-automation
sudo chown $USER:$USER /opt/ui-automation

# 克隆代码
cd /opt/ui-automation
git clone <your-repo-url> .

# 设置Python虚拟环境
cd backend
python3.11 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入API Keys

# 创建上传目录
mkdir -p uploads

# 使用PM2启动后端
pm2 start "uvicorn main:app --host 0.0.0.0 --port 8000" --name ui-automation-backend
```

### 4. 部署前端

```bash
# 构建前端
cd ../frontend
npm install
npm run build

# 复制构建文件到Nginx目录
sudo cp -r build/* /var/www/html/
```

### 5. 配置Nginx

```nginx
# /etc/nginx/sites-available/ui-automation
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /var/www/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # 后端API代理
    location /api/ {
        proxy_pass http://localhost:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # SSE支持
    location /analyze/stream {
        proxy_pass http://localhost:8000/analyze/stream;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # SSE特殊配置
        proxy_buffering off;
        proxy_cache off;
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        chunked_transfer_encoding off;
    }

    # 上传文件访问
    location /uploads/ {
        proxy_pass http://localhost:8000/uploads/;
    }
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/ui-automation /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔒 SSL/HTTPS 配置

### 使用 Let's Encrypt

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 监控和日志

### 1. 设置日志

```bash
# 创建日志目录
sudo mkdir -p /var/log/ui-automation

# 配置logrotate
sudo tee /etc/logrotate.d/ui-automation << EOF
/var/log/ui-automation/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
EOF
```

### 2. 监控脚本

```bash
#!/bin/bash
# monitor.sh - 系统监控脚本

# 检查后端服务
if ! pm2 list | grep -q "ui-automation-backend.*online"; then
    echo "后端服务异常，正在重启..."
    pm2 restart ui-automation-backend
fi

# 检查磁盘空间
DISK_USAGE=$(df /opt/ui-automation | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "磁盘空间不足: ${DISK_USAGE}%"
    # 清理旧的上传文件
    find /opt/ui-automation/backend/uploads -type f -mtime +7 -delete
fi

# 检查内存使用
MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
if [ $MEMORY_USAGE -gt 90 ]; then
    echo "内存使用过高: ${MEMORY_USAGE}%"
fi
```

### 3. 设置定时任务

```bash
# 添加到crontab
crontab -e

# 每5分钟检查一次
*/5 * * * * /opt/ui-automation/monitor.sh >> /var/log/ui-automation/monitor.log 2>&1
```

## 🔧 性能优化

### 1. 后端优化

```python
# backend/main.py 添加配置
app = FastAPI(
    title="图片分析智能体系统",
    description="基于AutoGen 0.6.2的多智能体图片分析系统",
    version="1.0.0",
    docs_url="/docs" if Config.DEBUG else None,  # 生产环境关闭文档
    redoc_url=None if not Config.DEBUG else "/redoc"
)

# 添加压缩中间件
from fastapi.middleware.gzip import GZipMiddleware
app.add_middleware(GZipMiddleware, minimum_size=1000)
```

### 2. 前端优化

```bash
# 构建时启用优化
GENERATE_SOURCEMAP=false npm run build

# 启用Nginx压缩
# 在nginx配置中添加:
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

## 🚀 自动化部署

### GitHub Actions 示例

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /opt/ui-automation
          git pull origin main
          
          # 更新后端
          cd backend
          source venv/bin/activate
          pip install -r requirements.txt
          pm2 restart ui-automation-backend
          
          # 更新前端
          cd ../frontend
          npm install
          npm run build
          sudo cp -r build/* /var/www/html/
          
          # 重载Nginx
          sudo systemctl reload nginx
```

## 🔍 故障排除

### 常见问题

1. **后端启动失败**
   ```bash
   # 检查日志
   pm2 logs ui-automation-backend
   
   # 检查端口占用
   sudo netstat -tlnp | grep :8000
   ```

2. **前端无法访问后端**
   ```bash
   # 检查Nginx配置
   sudo nginx -t
   
   # 检查防火墙
   sudo ufw status
   ```

3. **SSL证书问题**
   ```bash
   # 检查证书状态
   sudo certbot certificates
   
   # 手动续期
   sudo certbot renew
   ```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 检查系统日志
2. 查看应用日志
3. 确认网络连接
4. 验证API Keys配置

更多帮助请参考项目文档或提交Issue。
