# 图片分析智能体系统

基于 AutoGen 0.6.1 框架构建的多智能体图片分析系统，支持 UI 元素识别、交互分析和测试用例生成。

## 🌟 特性

- **多智能体协作**：基于 AutoGen 0.6.1 的 GraphFlow 实现智能体协作
- **流式输出**：基于 SSE 协议的实时分析过程展示
- **智能分析**：
  - 🔍 UI 元素识别智能体 (ui-tars)
  - 🎯 交互分析智能体 (ui-tars)  
  - 📝 用例生成智能体 (deepseek-chat)
- **现代化前端**：React + TypeScript + Ant Design
- **炫酷界面**：Framer Motion 动画 + 渐变背景

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │────│  后端 (FastAPI)  │────│ AutoGen 智能体   │
│                 │    │                 │    │                 │
│ • 图片上传       │    │ • SSE 流式输出   │    │ • 元素识别       │
│ • 实时对话       │    │ • GraphFlow     │    │ • 交互分析       │
│ • 结果展示       │    │ • 多智能体协调   │    │ • 用例生成       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Node.js 16+
- OpenAI API Key
- DeepSeek API Key
- UI-TARS API Key (可选)

### 🔧 解决依赖冲突（推荐）

如果遇到依赖冲突错误，请使用专门的修复脚本：

```bash
# 解决依赖冲突问题
python fix_dependencies.py
```

### 一键安装和测试

```bash
# 自动安装所有依赖并测试系统
python install_and_test.py
```

### 手动安装

#### 后端设置

1. **安装依赖**
```bash
cd backend
pip install -r requirements.txt
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，填入你的 API Keys
```

3. **测试 AutoGen**
```bash
python test_autogen.py
```

4. **启动后端服务**
```bash
python main.py
```

后端服务将在 `http://localhost:8000` 启动

#### 前端设置

1. **安装依赖**
```bash
cd frontend
npm install
```

2. **启动前端服务**
```bash
npm start
```

前端服务将在 `http://localhost:3000` 启动

### 一键启动

```bash
# 启动完整系统（前端+后端）
python start.py
```

## 📖 使用说明

### 1. 上传图片
- 支持拖拽上传或点击选择
- 支持 JPG、PNG、GIF、BMP、WebP 格式
- 最大文件大小 10MB

### 2. 开始分析
- 在对话框中输入对图片的描述或问题
- 系统将自动启动多智能体分析流程
- 实时查看各智能体的处理过程

### 3. 查看结果
- **概览**：统计信息和分析总结
- **UI元素**：识别的界面元素详情
- **交互分析**：用户交互行为分析
- **测试用例**：生成的测试用例

## 🤖 智能体说明

### 元素识别智能体
- **模型**：GPT-4o (支持视觉)
- **功能**：识别图片中的 UI 元素
- **输出**：元素类型、位置、属性、置信度

### 交互分析智能体  
- **模型**：GPT-4o
- **功能**：分析用户可能的交互行为
- **输出**：交互类型、操作流程、用户体验评估

### 用例生成智能体
- **模型**：DeepSeek Chat
- **功能**：生成全面的测试用例
- **输出**：功能测试、UI测试、兼容性测试等

## 🔧 配置说明

### 环境变量

```bash
# OpenAI 配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1

# DeepSeek 配置  
DEEPSEEK_API_KEY=your_deepseek_api_key
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1

# UI-TARS 配置 (可选)
UITARS_API_KEY=your_uitars_api_key
UITARS_BASE_URL=https://api.ui-tars.com/v1

# 应用配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,bmp,webp
```

## 📁 项目结构

```
ui-automation/
├── backend/                 # 后端代码
│   ├── agents/             # 智能体定义
│   ├── models/             # 数据模型
│   ├── services/           # 业务逻辑
│   ├── utils/              # 工具函数
│   └── main.py             # 主应用
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── components/     # React 组件
│   │   ├── services/       # API 服务
│   │   ├── types/          # TypeScript 类型
│   │   └── App.tsx         # 主应用
│   └── package.json
└── README.md
```

## 🛠️ 开发指南

### 添加新智能体

1. 在 `backend/agents/` 创建新的智能体类
2. 在 `backend/services/agent_service.py` 中注册智能体
3. 更新 GraphFlow 配置

### 自定义分析流程

修改 `AgentService._build_graph_flow()` 方法来调整智能体执行顺序和依赖关系。

### 扩展前端功能

在 `frontend/src/components/` 添加新组件，并在 `App.tsx` 中集成。

## 🔍 API 文档

### 主要接口

- `POST /upload` - 上传图片
- `POST /analyze/stream` - 流式分析图片 (SSE)
- `POST /analyze` - 分析图片 (非流式)
- `GET /files` - 获取文件列表
- `DELETE /files/{path}` - 删除文件

### SSE 消息格式

```typescript
interface StreamMessage {
  id: string;
  type: MessageType;
  agent?: AgentType;
  content: string;
  timestamp: string;
  metadata?: Record<string, any>;
}
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🔍 故障排除

### 依赖安装问题

1. **依赖冲突错误**
   ```
   ERROR: Cannot install autogen-agentchat and pillow==10.1.0 because these package versions have conflicting dependencies.
   ```

   **解决方案**：
   ```bash
   # 使用专门的修复脚本
   python fix_dependencies.py

   # 或者手动解决
   pip install pillow  # 让pip选择兼容版本
   pip install autogen-agentchat autogen-ext[openai]
   ```

2. **AutoGen 安装失败**
   ```bash
   # 尝试单独安装
   pip install autogen-agentchat==0.6.1
   pip install autogen-ext[openai]==0.6.1

   # 如果版本不存在，安装最新版本
   pip install autogen-agentchat
   pip install autogen-ext[openai]
   ```

2. **Python 版本问题**
   ```bash
   # 检查 Python 版本
   python --version

   # 如果版本过低，使用 pyenv 或 conda 升级
   ```

3. **网络问题**
   ```bash
   # 使用国内镜像
   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ -r requirements.txt
   ```

### 运行时问题

1. **API Key 错误**
   ```bash
   # 检查 .env 文件配置
   cat backend/.env

   # 测试 API 连接
   python backend/test_autogen.py
   ```

2. **端口占用**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :8000
   lsof -i :8000

   # 杀死占用进程
   kill -9 <PID>
   ```

3. **前端无法访问后端**
   ```bash
   # 检查后端是否启动
   curl http://localhost:8000/health

   # 检查防火墙设置
   ```

### 常见错误

1. **ModuleNotFoundError: No module named 'autogen_agentchat'**
   ```bash
   # 重新安装 AutoGen
   pip uninstall autogen-agentchat autogen-ext
   pip install autogen-agentchat==0.6.1 autogen-ext[openai]==0.6.1
   ```

2. **OpenAI API 错误**
   ```bash
   # 检查 API Key 是否有效
   # 检查账户余额
   # 确认模型访问权限
   ```

3. **内存不足**
   ```bash
   # 监控内存使用
   htop

   # 增加虚拟内存 (Linux)
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

## 🙏 致谢

- [AutoGen](https://github.com/microsoft/autogen) - 多智能体框架
- [FastAPI](https://fastapi.tiangolo.com/) - 现代 Python Web 框架
- [Ant Design](https://ant.design/) - 企业级 UI 设计语言
